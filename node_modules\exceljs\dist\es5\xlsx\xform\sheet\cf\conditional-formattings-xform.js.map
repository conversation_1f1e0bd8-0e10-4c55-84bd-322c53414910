{"version": 3, "file": "conditional-formattings-xform.js", "names": ["BaseXform", "require", "ConditionalFormattingXform", "ConditionalFormattingsXform", "constructor", "cfXform", "tag", "reset", "model", "prepare", "options", "nextPriority", "reduce", "p", "cf", "Math", "max", "rules", "map", "rule", "priority", "for<PERSON>ach", "style", "dxfId", "styles", "addDxfStyle", "render", "xmlStream", "parseOpen", "node", "parser", "name", "parseText", "text", "parseClose", "push", "undefined", "reconcile", "getDxfStyle", "module", "exports"], "sources": ["../../../../../../lib/xlsx/xform/sheet/cf/conditional-formattings-xform.js"], "sourcesContent": ["const BaseXform = require('../../base-xform');\n\nconst ConditionalFormattingXform = require('./conditional-formatting-xform');\n\nclass ConditionalFormattingsXform extends BaseXform {\n  constructor() {\n    super();\n\n    this.cfXform = new ConditionalFormattingXform();\n  }\n\n  get tag() {\n    return 'conditionalFormatting';\n  }\n\n  reset() {\n    this.model = [];\n  }\n\n  prepare(model, options) {\n    // ensure each rule has a priority value\n    let nextPriority = model.reduce(\n      (p, cf) => Math.max(p, ...cf.rules.map(rule => rule.priority || 0)),\n      1\n    );\n    model.forEach(cf => {\n      cf.rules.forEach(rule => {\n        if (!rule.priority) {\n          rule.priority = nextPriority++;\n        }\n\n        if (rule.style) {\n          rule.dxfId = options.styles.addDxfStyle(rule.style);\n        }\n      });\n    });\n  }\n\n  render(xmlStream, model) {\n    model.forEach(cf => {\n      this.cfXform.render(xmlStream, cf);\n    });\n  }\n\n  parseOpen(node) {\n    if (this.parser) {\n      this.parser.parseOpen(node);\n      return true;\n    }\n\n    switch (node.name) {\n      case 'conditionalFormatting':\n        this.parser = this.cfXform;\n        this.parser.parseOpen(node);\n        return true;\n\n      default:\n        return false;\n    }\n  }\n\n  parseText(text) {\n    if (this.parser) {\n      this.parser.parseText(text);\n    }\n  }\n\n  parseClose(name) {\n    if (this.parser) {\n      if (!this.parser.parseClose(name)) {\n        this.model.push(this.parser.model);\n        this.parser = undefined;\n        return false;\n      }\n      return true;\n    }\n    return false;\n  }\n\n  reconcile(model, options) {\n    model.forEach(cf => {\n      cf.rules.forEach(rule => {\n        if (rule.dxfId !== undefined) {\n          rule.style = options.styles.getDxfStyle(rule.dxfId);\n          delete rule.dxfId;\n        }\n      });\n    });\n  }\n}\n\nmodule.exports = ConditionalFormattingsXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AAE7C,MAAMC,0BAA0B,GAAGD,OAAO,CAAC,gCAAgC,CAAC;AAE5E,MAAME,2BAA2B,SAASH,SAAS,CAAC;EAClDI,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,OAAO,GAAG,IAAIH,0BAA0B,CAAC,CAAC;EACjD;EAEA,IAAII,GAAGA,CAAA,EAAG;IACR,OAAO,uBAAuB;EAChC;EAEAC,KAAKA,CAAA,EAAG;IACN,IAAI,CAACC,KAAK,GAAG,EAAE;EACjB;EAEAC,OAAOA,CAACD,KAAK,EAAEE,OAAO,EAAE;IACtB;IACA,IAAIC,YAAY,GAAGH,KAAK,CAACI,MAAM,CAC7B,CAACC,CAAC,EAAEC,EAAE,KAAKC,IAAI,CAACC,GAAG,CAACH,CAAC,EAAE,GAAGC,EAAE,CAACG,KAAK,CAACC,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,QAAQ,IAAI,CAAC,CAAC,CAAC,EACnE,CACF,CAAC;IACDZ,KAAK,CAACa,OAAO,CAACP,EAAE,IAAI;MAClBA,EAAE,CAACG,KAAK,CAACI,OAAO,CAACF,IAAI,IAAI;QACvB,IAAI,CAACA,IAAI,CAACC,QAAQ,EAAE;UAClBD,IAAI,CAACC,QAAQ,GAAGT,YAAY,EAAE;QAChC;QAEA,IAAIQ,IAAI,CAACG,KAAK,EAAE;UACdH,IAAI,CAACI,KAAK,GAAGb,OAAO,CAACc,MAAM,CAACC,WAAW,CAACN,IAAI,CAACG,KAAK,CAAC;QACrD;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAI,MAAMA,CAACC,SAAS,EAAEnB,KAAK,EAAE;IACvBA,KAAK,CAACa,OAAO,CAACP,EAAE,IAAI;MAClB,IAAI,CAACT,OAAO,CAACqB,MAAM,CAACC,SAAS,EAAEb,EAAE,CAAC;IACpC,CAAC,CAAC;EACJ;EAEAc,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACC,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;MAC3B,OAAO,IAAI;IACb;IAEA,QAAQA,IAAI,CAACE,IAAI;MACf,KAAK,uBAAuB;QAC1B,IAAI,CAACD,MAAM,GAAG,IAAI,CAACzB,OAAO;QAC1B,IAAI,CAACyB,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;QAC3B,OAAO,IAAI;MAEb;QACE,OAAO,KAAK;IAChB;EACF;EAEAG,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACH,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACE,SAAS,CAACC,IAAI,CAAC;IAC7B;EACF;EAEAC,UAAUA,CAACH,IAAI,EAAE;IACf,IAAI,IAAI,CAACD,MAAM,EAAE;MACf,IAAI,CAAC,IAAI,CAACA,MAAM,CAACI,UAAU,CAACH,IAAI,CAAC,EAAE;QACjC,IAAI,CAACvB,KAAK,CAAC2B,IAAI,CAAC,IAAI,CAACL,MAAM,CAACtB,KAAK,CAAC;QAClC,IAAI,CAACsB,MAAM,GAAGM,SAAS;QACvB,OAAO,KAAK;MACd;MACA,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAEAC,SAASA,CAAC7B,KAAK,EAAEE,OAAO,EAAE;IACxBF,KAAK,CAACa,OAAO,CAACP,EAAE,IAAI;MAClBA,EAAE,CAACG,KAAK,CAACI,OAAO,CAACF,IAAI,IAAI;QACvB,IAAIA,IAAI,CAACI,KAAK,KAAKa,SAAS,EAAE;UAC5BjB,IAAI,CAACG,KAAK,GAAGZ,OAAO,CAACc,MAAM,CAACc,WAAW,CAACnB,IAAI,CAACI,KAAK,CAAC;UACnD,OAAOJ,IAAI,CAACI,KAAK;QACnB;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;AACF;AAEAgB,MAAM,CAACC,OAAO,GAAGrC,2BAA2B"}