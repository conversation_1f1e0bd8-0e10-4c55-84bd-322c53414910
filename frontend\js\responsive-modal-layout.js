/**
 * 响应式模态框布局解决方案
 * 专门解决总监审核模态框的布局问题，提供多种布局模式
 */

// 布局模式枚举
const LAYOUT_MODES = {
    AUTO: 'auto',           // 自动模式
    COMPACT: 'compact',     // 紧凑模式
    SPLIT: 'split',         // 分屏模式
    TABBED: 'tabbed'        // 标签页模式
};

// 当前布局模式
let currentLayoutMode = LAYOUT_MODES.AUTO;

/**
 * 检测最佳布局模式
 */
function detectOptimalLayoutMode() {
    const viewportHeight = window.innerHeight;
    const viewportWidth = window.innerWidth;

    // 移动设备或小屏幕
    if (viewportWidth < 768 || viewportHeight < 600) {
        return LAYOUT_MODES.COMPACT;
    }

    // 中等屏幕
    if (viewportHeight < 800) {
        return LAYOUT_MODES.SPLIT;
    }

    // 大屏幕
    return LAYOUT_MODES.AUTO;
}

/**
 * 应用紧凑模式布局
 */
function applyCompactLayout() {
    const detailModal = document.getElementById('detailModal');
    if (!detailModal) return;

    const modalContainer = detailModal.querySelector('.relative');
    if (!modalContainer) return;

    // 设置紧凑模式样式
    modalContainer.style.maxHeight = '98vh';
    modalContainer.style.margin = '1vh auto';

    // 压缩内容区域
    const contentArea = modalContainer.querySelector('.flex-grow');
    if (contentArea) {
        contentArea.style.maxHeight = '50vh';
        contentArea.style.overflowY = 'auto';
    }

    // 优化审批操作区域
    const actionsArea = document.getElementById('detail-approval-actions');
    if (actionsArea && !actionsArea.classList.contains('hidden')) {
        actionsArea.style.maxHeight = '45vh';
        actionsArea.style.overflowY = 'auto';

        // 强制折叠经理选择区域
        const managersContent = document.getElementById('managers-selection-content');
        if (managersContent && !managersContent.classList.contains('hidden')) {
            managersContent.classList.add('hidden');
            updateManagersToggleIcon(false);
        }
    }

    console.log('已应用紧凑模式布局');
}

/**
 * 应用分屏模式布局
 */
function applySplitLayout() {
    const detailModal = document.getElementById('detailModal');
    if (!detailModal) return;

    const modalContainer = detailModal.querySelector('.relative');
    if (!modalContainer) return;

    // 检查是否需要分屏
    const actionsArea = document.getElementById('detail-approval-actions');
    if (!actionsArea || actionsArea.classList.contains('hidden')) {
        // 如果没有审批操作，使用普通布局
        applyAutoLayout();
        return;
    }

    // 创建分屏布局
    modalContainer.style.maxHeight = '95vh';
    modalContainer.style.display = 'grid';
    modalContainer.style.gridTemplateRows = 'auto auto 1fr auto';

    const contentArea = modalContainer.querySelector('.flex-grow');
    if (contentArea) {
        contentArea.style.height = '45vh';
        contentArea.style.overflowY = 'auto';
    }

    actionsArea.style.height = '40vh';
    actionsArea.style.overflowY = 'auto';

    console.log('已应用分屏模式布局');
}

/**
 * 应用自动模式布局
 */
function applyAutoLayout() {
    const detailModal = document.getElementById('detailModal');
    if (!detailModal) return;

    const modalContainer = detailModal.querySelector('.relative');
    if (!modalContainer) return;

    // 检查是否需要特殊布局（总监审批且有经理选择区域）
    const needsSpecial = window.modalLayoutOptimizer &&
                        typeof window.modalLayoutOptimizer.needsSpecial === 'function' &&
                        window.modalLayoutOptimizer.needsSpecial();

    if (!needsSpecial) {
        // 对于其他角色，使用标准布局
        if (window.modalLayoutOptimizer && typeof window.modalLayoutOptimizer.standardLayout === 'function') {
            window.modalLayoutOptimizer.standardLayout();
        } else {
            // 备用标准布局
            modalContainer.style.maxHeight = '90vh';
            modalContainer.style.margin = '2vh auto';
            modalContainer.style.display = 'flex';
            modalContainer.style.flexDirection = 'column';

            const contentArea = modalContainer.querySelector('.flex-grow');
            if (contentArea) {
                contentArea.style.height = '';
                contentArea.style.maxHeight = '';
                contentArea.style.flex = '1';
                contentArea.style.overflowY = 'auto';
            }

            const actionsArea = document.getElementById('detail-approval-actions');
            if (actionsArea && !actionsArea.classList.contains('hidden')) {
                actionsArea.style.height = '';
                actionsArea.style.maxHeight = '300px';
                actionsArea.style.flexShrink = '0';
                actionsArea.style.overflowY = 'auto';
            }
        }
        console.log('已应用标准自动模式布局');
        return;
    }

    // 总监特殊布局逻辑
    modalContainer.style.maxHeight = '90vh';
    modalContainer.style.margin = '2vh auto';
    modalContainer.style.display = 'flex';
    modalContainer.style.gridTemplateRows = '';

    const contentArea = modalContainer.querySelector('.flex-grow');
    if (contentArea) {
        contentArea.style.height = '';
        contentArea.style.maxHeight = '';
    }

    const actionsArea = document.getElementById('detail-approval-actions');
    if (actionsArea) {
        actionsArea.style.height = '';
        actionsArea.style.maxHeight = '40vh';
    }

    console.log('已应用总监特殊自动模式布局');
}

/**
 * 更新经理选择区域的折叠图标
 */
function updateManagersToggleIcon(isExpanded) {
    const icon = document.getElementById('managers-toggle-icon');
    const summary = document.getElementById('detail-selected-managers-summary');

    if (icon) {
        icon.style.transform = isExpanded ? 'rotate(180deg)' : 'rotate(0deg)';
    }

    if (summary) {
        summary.textContent = isExpanded ? '点击收起' : '点击展开选择';
    }
}

/**
 * 应用指定的布局模式
 */
function applyLayoutMode(mode) {
    currentLayoutMode = mode;

    switch (mode) {
        case LAYOUT_MODES.COMPACT:
            applyCompactLayout();
            break;
        case LAYOUT_MODES.SPLIT:
            applySplitLayout();
            break;
        case LAYOUT_MODES.AUTO:
        default:
            applyAutoLayout();
            break;
    }

    // 触发布局调整事件
    window.dispatchEvent(new CustomEvent('modalLayoutChanged', {
        detail: { mode: mode }
    }));
}

/**
 * 智能布局调整
 */
function smartResponsiveLayout() {
    const optimalMode = detectOptimalLayoutMode();

    // 如果当前模式不是最优模式，切换到最优模式
    if (currentLayoutMode !== optimalMode) {
        applyLayoutMode(optimalMode);
    }
}

/**
 * 创建布局模式切换器
 */
function createLayoutModeSwitcher() {
    const detailModal = document.getElementById('detailModal');
    if (!detailModal) return;

    const modalContainer = detailModal.querySelector('.relative');
    if (!modalContainer) return;

    // 检查是否已存在切换器
    if (document.getElementById('layout-mode-switcher')) return;

    // 创建切换器HTML
    const switcherHTML = `
        <div id="layout-mode-switcher" class="absolute top-4 right-16 z-10 bg-white border rounded-lg shadow-lg p-2 hidden">
            <div class="text-xs text-gray-600 mb-2">布局模式</div>
            <div class="flex space-x-1">
                <button onclick="switchLayoutMode('auto')" class="layout-mode-btn px-2 py-1 text-xs rounded hover:bg-blue-100" data-mode="auto">自动</button>
                <button onclick="switchLayoutMode('compact')" class="layout-mode-btn px-2 py-1 text-xs rounded hover:bg-blue-100" data-mode="compact">紧凑</button>
                <button onclick="switchLayoutMode('split')" class="layout-mode-btn px-2 py-1 text-xs rounded hover:bg-blue-100" data-mode="split">分屏</button>
            </div>
        </div>
        <button id="layout-toggle-btn" onclick="toggleLayoutSwitcher()" class="absolute top-4 right-12 z-10 bg-blue-600 text-white p-1 rounded hover:bg-blue-700" title="切换布局模式">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
        </button>
    `;

    modalContainer.insertAdjacentHTML('afterbegin', switcherHTML);

    // 更新当前模式按钮状态
    updateLayoutSwitcherState();
}

/**
 * 切换布局模式切换器的显示状态
 */
function toggleLayoutSwitcher() {
    const switcher = document.getElementById('layout-mode-switcher');
    if (switcher) {
        switcher.classList.toggle('hidden');
    }
}

/**
 * 切换布局模式
 */
function switchLayoutMode(mode) {
    applyLayoutMode(mode);
    updateLayoutSwitcherState();

    // 隐藏切换器
    const switcher = document.getElementById('layout-mode-switcher');
    if (switcher) {
        switcher.classList.add('hidden');
    }
}

/**
 * 更新布局切换器状态
 */
function updateLayoutSwitcherState() {
    const buttons = document.querySelectorAll('.layout-mode-btn');
    buttons.forEach(btn => {
        const mode = btn.getAttribute('data-mode');
        if (mode === currentLayoutMode) {
            btn.classList.add('bg-blue-600', 'text-white');
            btn.classList.remove('hover:bg-blue-100');
        } else {
            btn.classList.remove('bg-blue-600', 'text-white');
            btn.classList.add('hover:bg-blue-100');
        }
    });
}

/**
 * 初始化响应式模态框布局
 */
function initResponsiveModalLayout() {
    // 监听窗口大小变化
    window.addEventListener('resize', () => {
        setTimeout(smartResponsiveLayout, 100);
    });

    // 监听模态框打开事件
    const originalShowDetail = window.showDetail;
    if (typeof originalShowDetail === 'function') {
        window.showDetail = function(appId) {
            originalShowDetail(appId);
            setTimeout(() => {
                // 确保经理选择区域重置为折叠状态
                if (typeof resetManagersSelectionState === 'function') {
                    resetManagersSelectionState();
                }

                createLayoutModeSwitcher();
                smartResponsiveLayout();
            }, 100);
        };
    }

    console.log('响应式模态框布局已初始化');
}

// 导出全局函数
window.switchLayoutMode = switchLayoutMode;
window.toggleLayoutSwitcher = toggleLayoutSwitcher;

// 导出模块
window.responsiveModalLayout = {
    init: initResponsiveModalLayout,
    applyMode: applyLayoutMode,
    smartAdjust: smartResponsiveLayout,
    modes: LAYOUT_MODES
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(initResponsiveModalLayout, 200);
});
