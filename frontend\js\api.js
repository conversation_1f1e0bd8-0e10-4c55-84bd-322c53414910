/**
 * API请求工具
 * 包含超时设置、自动重试和错误处理
 */

// 网络配置常量 - 优化加载速度
const API_CONFIG = {
    // 请求超时时间（毫秒）- 减少超时时间提升响应速度
    timeout: 8000,

    // 重试配置
    retry: {
        // 最大重试次数 - 减少重试次数提升加载速度
        maxRetries: 2,
        // 重试延迟（毫秒）- 减少重试延迟
        retryDelay: 500,
        // 需要重试的HTTP状态码
        retryStatusCodes: [408, 429, 500, 502, 503, 504, 0] // 添加0状态码（网络错误）
    },

    // 网络兼容性配置
    network: {
        // 支持代理和VPN
        supportProxy: true,
        // 自动检测网络状态
        autoDetectNetwork: true,
        // 网络质量检测间隔（毫秒）
        qualityCheckInterval: 30000
    },

    // 调试模式
    debug: true
};

/**
 * 发送API请求
 * @param {string} url - 请求URL
 * @param {Object} options - 请求选项
 * @param {string} options.method - 请求方法 (GET, POST, PUT, DELETE)
 * @param {Object} options.data - 请求数据
 * @param {Object} options.headers - 请求头
 * @param {number} options.timeout - 请求超时时间（毫秒）
 * @param {Object} options.retry - 重试配置
 * @returns {Promise<any>} 响应数据
 */
async function apiRequest(url, options = {}) {
    const {
        method = 'GET',
        data = null,
        headers = {},
        timeout = API_CONFIG.timeout,
        retry = API_CONFIG.retry
    } = options;

    // 合并默认头部
    const requestHeaders = {
        'Content-Type': 'application/json',
        ...headers
    };

    // 构建请求选项
    const fetchOptions = {
        method,
        headers: requestHeaders,
        credentials: 'same-origin',
        // 添加缓存控制，避免代理缓存问题
        cache: 'no-cache',
        // 添加模式设置，确保跨域兼容
        mode: 'cors'
    };

    // 添加请求体
    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
        fetchOptions.body = JSON.stringify(data);
    }

    // 创建AbortController用于超时控制
    const controller = new AbortController();
    fetchOptions.signal = controller.signal;

    // 设置超时
    const timeoutId = setTimeout(() => {
        controller.abort();
    }, timeout);

    // 调试日志
    if (API_CONFIG.debug) {
        console.log(`API请求: ${method} ${url}`, {
            data,
            timeout,
            retry: { ...retry }
        });
    }

    // 重试计数器
    let retries = 0;

    // 执行请求（带重试）
    while (true) {
        try {
            const response = await fetch(url, fetchOptions);

            // 清除超时
            clearTimeout(timeoutId);

            // 检查是否需要重试
            if (!response.ok && retry.retryStatusCodes.includes(response.status) && retries < retry.maxRetries) {
                retries++;

                if (API_CONFIG.debug) {
                    console.log(`请求失败，状态码: ${response.status}，准备第 ${retries} 次重试...`);
                }

                // 等待重试延迟
                await new Promise(resolve => setTimeout(resolve, retry.retryDelay));
                continue;
            }

            // 尝试解析JSON响应
            try {
                const data = await response.json();

                if (!response.ok) {
                    throw {
                        status: response.status,
                        message: data.message || '请求失败',
                        data
                    };
                }

                return data;
            } catch (parseError) {
                // JSON解析错误
                if (response.ok) {
                    // 如果响应成功但JSON解析失败，返回文本内容
                    const text = await response.text();
                    return { text };
                } else {
                    throw {
                        status: response.status,
                        message: '无法解析响应数据',
                        error: parseError
                    };
                }
            }
        } catch (error) {
            // 清除超时
            clearTimeout(timeoutId);

            // 处理请求被中止的情况（超时）
            if (error.name === 'AbortError') {
                if (retries < retry.maxRetries) {
                    retries++;

                    if (API_CONFIG.debug) {
                        console.log(`请求超时，准备第 ${retries} 次重试...`);
                    }

                    // 创建新的AbortController
                    const controller = new AbortController();
                    fetchOptions.signal = controller.signal;

                    // 设置新的超时
                    const timeoutId = setTimeout(() => {
                        controller.abort();
                    }, timeout);

                    // 等待重试延迟
                    await new Promise(resolve => setTimeout(resolve, retry.retryDelay));
                    continue;
                } else {
                    throw {
                        status: 'TIMEOUT',
                        message: '请求超时，请检查网络连接'
                    };
                }
            }

            // 如果是网络错误且还有重试次数
            if ((error.message && (error.message.includes('network') ||
                                  error.message.includes('Failed to fetch') ||
                                  error.message.includes('ERR_NETWORK') ||
                                  error.message.includes('ERR_INTERNET_DISCONNECTED'))) &&
                retries < retry.maxRetries) {
                retries++;

                if (API_CONFIG.debug) {
                    console.log(`网络错误，准备第 ${retries} 次重试...`, error.message);
                }

                // 等待重试延迟，对于网络错误增加延迟时间
                const networkRetryDelay = retry.retryDelay * (retries + 1);
                await new Promise(resolve => setTimeout(resolve, networkRetryDelay));
                continue;
            }

            // 其他错误或重试次数已用完
            throw error;
        }
    }
}

// 导出API方法
window.api = {
    /**
     * 发送GET请求
     * @param {string} url - 请求URL
     * @param {Object} options - 请求选项
     * @returns {Promise<any>} 响应数据
     */
    get: (url, options = {}) => {
        return apiRequest(url, { ...options, method: 'GET' });
    },

    /**
     * 发送POST请求
     * @param {string} url - 请求URL
     * @param {Object} data - 请求数据
     * @param {Object} options - 请求选项
     * @returns {Promise<any>} 响应数据
     */
    post: (url, data, options = {}) => {
        return apiRequest(url, { ...options, method: 'POST', data });
    },

    /**
     * 发送PUT请求
     * @param {string} url - 请求URL
     * @param {Object} data - 请求数据
     * @param {Object} options - 请求选项
     * @returns {Promise<any>} 响应数据
     */
    put: (url, data, options = {}) => {
        return apiRequest(url, { ...options, method: 'PUT', data });
    },

    /**
     * 发送DELETE请求
     * @param {string} url - 请求URL
     * @param {Object} options - 请求选项
     * @returns {Promise<any>} 响应数据
     */
    delete: (url, options = {}) => {
        return apiRequest(url, { ...options, method: 'DELETE' });
    }
};
