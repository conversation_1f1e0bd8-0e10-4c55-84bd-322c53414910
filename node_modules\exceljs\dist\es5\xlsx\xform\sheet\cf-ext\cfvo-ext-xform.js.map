{"version": 3, "file": "cfvo-ext-xform.js", "names": ["CompositeXform", "require", "FExtXform", "CfvoExtXform", "constructor", "map", "fExtXform", "tag", "render", "xmlStream", "model", "openNode", "type", "value", "undefined", "closeNode", "createNewModel", "node", "attributes", "onParserClose", "name", "parser", "parseFloat", "module", "exports"], "sources": ["../../../../../../lib/xlsx/xform/sheet/cf-ext/cfvo-ext-xform.js"], "sourcesContent": ["const CompositeXform = require('../../composite-xform');\n\nconst FExtXform = require('./f-ext-xform');\n\nclass CfvoExtXform extends CompositeXform {\n  constructor() {\n    super();\n\n    this.map = {\n      'xm:f': (this.fExtXform = new FExtXform()),\n    };\n  }\n\n  get tag() {\n    return 'x14:cfvo';\n  }\n\n  render(xmlStream, model) {\n    xmlStream.openNode(this.tag, {\n      type: model.type,\n    });\n    if (model.value !== undefined) {\n      this.fExtXform.render(xmlStream, model.value);\n    }\n    xmlStream.closeNode();\n  }\n\n  createNewModel(node) {\n    return {\n      type: node.attributes.type,\n    };\n  }\n\n  onParserClose(name, parser) {\n    switch (name) {\n      case 'xm:f':\n        this.model.value = parser.model ? parseFloat(parser.model) : 0;\n        break;\n    }\n  }\n}\n\nmodule.exports = CfvoExtXform;\n"], "mappings": ";;AAAA,MAAMA,cAAc,GAAGC,OAAO,CAAC,uBAAuB,CAAC;AAEvD,MAAMC,SAAS,GAAGD,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAME,YAAY,SAASH,cAAc,CAAC;EACxCI,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,GAAG,GAAG;MACT,MAAM,EAAG,IAAI,CAACC,SAAS,GAAG,IAAIJ,SAAS,CAAC;IAC1C,CAAC;EACH;EAEA,IAAIK,GAAGA,CAAA,EAAG;IACR,OAAO,UAAU;EACnB;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvBD,SAAS,CAACE,QAAQ,CAAC,IAAI,CAACJ,GAAG,EAAE;MAC3BK,IAAI,EAAEF,KAAK,CAACE;IACd,CAAC,CAAC;IACF,IAAIF,KAAK,CAACG,KAAK,KAAKC,SAAS,EAAE;MAC7B,IAAI,CAACR,SAAS,CAACE,MAAM,CAACC,SAAS,EAAEC,KAAK,CAACG,KAAK,CAAC;IAC/C;IACAJ,SAAS,CAACM,SAAS,CAAC,CAAC;EACvB;EAEAC,cAAcA,CAACC,IAAI,EAAE;IACnB,OAAO;MACLL,IAAI,EAAEK,IAAI,CAACC,UAAU,CAACN;IACxB,CAAC;EACH;EAEAO,aAAaA,CAACC,IAAI,EAAEC,MAAM,EAAE;IAC1B,QAAQD,IAAI;MACV,KAAK,MAAM;QACT,IAAI,CAACV,KAAK,CAACG,KAAK,GAAGQ,MAAM,CAACX,KAAK,GAAGY,UAAU,CAACD,MAAM,CAACX,KAAK,CAAC,GAAG,CAAC;QAC9D;IACJ;EACF;AACF;AAEAa,MAAM,CAACC,OAAO,GAAGrB,YAAY"}