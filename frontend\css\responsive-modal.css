/**
 * 响应式模态框样式
 * 支持多种布局模式，解决总监审核模态框的布局问题
 */

/* 基础模态框优化 */
.detail-modal-optimized {
    transition: all 0.3s ease-in-out;
}

.detail-modal-content {
    transition: height 0.3s ease-in-out, max-height 0.3s ease-in-out;
}

.detail-modal-actions {
    transition: height 0.3s ease-in-out, max-height 0.3s ease-in-out;
}

/* 标准布局样式（适用于厂长、经理、CEO等其他角色） */
.modal-standard-layout {
    display: flex !important;
    flex-direction: column !important;
    max-height: 90vh !important;
}

.modal-standard-layout .flex-grow {
    flex: 1 !important;
    overflow-y: auto !important;
    min-height: 0 !important;
}

.modal-standard-layout #detail-approval-actions {
    flex-shrink: 0 !important;
    max-height: 300px !important;
    overflow-y: auto !important;
    border-top: 1px solid #e5e7eb !important;
    background-color: #f9fafb !important;
}

/* 经理选择区域优化 */
.managers-selection-optimized {
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    background-color: #f9fafb;
    transition: all 0.3s ease-in-out;
}

.managers-list-compact {
    max-height: 120px !important;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f1f5f9;
}

.managers-list-compact::-webkit-scrollbar {
    width: 6px;
}

.managers-list-compact::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

.managers-list-compact::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.managers-list-compact::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* 可折叠经理选择区域 */
.managers-toggle-header {
    cursor: pointer;
    user-select: none;
    transition: background-color 0.2s ease-in-out;
}

.managers-toggle-header:hover {
    background-color: #dbeafe !important;
}

.managers-toggle-icon {
    transition: transform 0.3s ease-in-out;
}

.managers-selection-content {
    transition: all 0.3s ease-in-out;
    overflow: hidden;
}

.managers-selection-content.hidden {
    max-height: 0;
    padding: 0;
    margin: 0;
    opacity: 0;
}

.managers-selection-content:not(.hidden) {
    max-height: 300px;
    opacity: 1;
}

/* 布局模式切换器 */
.layout-mode-switcher {
    backdrop-filter: blur(4px);
    background-color: rgba(255, 255, 255, 0.95);
    border: 1px solid #e5e7eb;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.layout-mode-btn {
    transition: all 0.2s ease-in-out;
    font-weight: 500;
    min-width: 40px;
    text-align: center;
}

.layout-mode-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.layout-mode-btn.active {
    background-color: #2563eb !important;
    color: white !important;
    box-shadow: 0 2px 4px rgba(37, 99, 235, 0.3);
}

#layout-toggle-btn {
    transition: all 0.2s ease-in-out;
}

#layout-toggle-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(37, 99, 235, 0.3);
}

/* 紧凑模式样式 */
.modal-compact-mode .detail-modal-content {
    padding: 0.75rem;
}

.modal-compact-mode .bg-gray-50 {
    padding: 0.75rem;
    margin-bottom: 0.5rem;
}

.modal-compact-mode h3 {
    font-size: 1rem;
    margin-bottom: 0.5rem;
}

.modal-compact-mode .space-y-4 > * + * {
    margin-top: 0.75rem;
}

.modal-compact-mode .grid {
    gap: 0.75rem;
}

/* 分屏模式样式 */
.modal-split-mode {
    display: grid !important;
    grid-template-rows: auto auto 1fr auto;
    gap: 0;
}

.modal-split-mode .detail-modal-content {
    border-bottom: 1px solid #e5e7eb;
    margin-bottom: 0;
}

.modal-split-mode .detail-modal-actions {
    border-top: 1px solid #e5e7eb;
    margin-top: 0;
}

/* 响应式断点 */
@media (max-width: 768px) {
    .detail-modal-optimized {
        margin: 0.5rem;
        max-height: 98vh !important;
    }

    .managers-list-compact {
        max-height: 100px !important;
    }

    .layout-mode-switcher {
        position: fixed !important;
        top: auto !important;
        bottom: 1rem !important;
        right: 1rem !important;
        left: 1rem !important;
        width: auto !important;
    }

    #layout-toggle-btn {
        position: fixed !important;
        bottom: 1rem !important;
        right: 1rem !important;
        top: auto !important;
    }
}

@media (max-height: 600px) {
    .detail-modal-optimized {
        max-height: 95vh !important;
    }

    .detail-modal-content {
        max-height: 40vh !important;
    }

    .detail-modal-actions {
        max-height: 50vh !important;
    }

    .managers-list-compact {
        max-height: 80px !important;
    }
}

/* 平滑滚动 */
.detail-modal-content,
.detail-modal-actions,
.managers-list-compact {
    scroll-behavior: smooth;
}

/* 焦点样式优化 */
.managers-toggle-header:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

.layout-mode-btn:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* 加载动画 */
.modal-layout-loading {
    position: relative;
    overflow: hidden;
}

.modal-layout-loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, #3b82f6, transparent);
    animation: loading-sweep 1.5s infinite;
    z-index: 1000;
}

@keyframes loading-sweep {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* 状态指示器 */
.managers-selection-status {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    transition: all 0.2s ease-in-out;
}

.managers-selection-status.confirmed {
    background-color: #dcfce7;
    color: #166534;
}

.managers-selection-status.pending {
    background-color: #fef3c7;
    color: #92400e;
}

/* 工具提示样式 */
.tooltip {
    position: relative;
}

.tooltip::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #1f2937;
    color: white;
    padding: 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s ease-in-out;
    z-index: 1000;
}

.tooltip:hover::before {
    opacity: 1;
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
    .detail-modal-optimized,
    .detail-modal-content,
    .detail-modal-actions,
    .managers-toggle-icon,
    .managers-selection-content,
    .layout-mode-btn {
        transition: none;
    }

    .modal-layout-loading::before {
        animation: none;
    }
}
