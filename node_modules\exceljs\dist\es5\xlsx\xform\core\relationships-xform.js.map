{"version": 3, "file": "relationships-xform.js", "names": ["XmlStream", "require", "BaseXform", "RelationshipXform", "RelationshipsXform", "constructor", "map", "Relationship", "render", "xmlStream", "model", "_values", "openXml", "StdDocAttributes", "openNode", "RELATIONSHIPS_ATTRIBUTES", "for<PERSON>ach", "relationship", "closeNode", "parseOpen", "node", "parser", "name", "Error", "JSON", "stringify", "parseText", "text", "parseClose", "push", "undefined", "xmlns", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/core/relationships-xform.js"], "sourcesContent": ["const XmlStream = require('../../../utils/xml-stream');\nconst BaseXform = require('../base-xform');\n\nconst RelationshipXform = require('./relationship-xform');\n\nclass RelationshipsXform extends BaseXform {\n  constructor() {\n    super();\n\n    this.map = {\n      Relationship: new RelationshipXform(),\n    };\n  }\n\n  render(xmlStream, model) {\n    model = model || this._values;\n    xmlStream.openXml(XmlStream.StdDocAttributes);\n    xmlStream.openNode('Relationships', RelationshipsXform.RELATIONSHIPS_ATTRIBUTES);\n\n    model.forEach(relationship => {\n      this.map.Relationship.render(xmlStream, relationship);\n    });\n\n    xmlStream.closeNode();\n  }\n\n  parseOpen(node) {\n    if (this.parser) {\n      this.parser.parseOpen(node);\n      return true;\n    }\n    switch (node.name) {\n      case 'Relationships':\n        this.model = [];\n        return true;\n      default:\n        this.parser = this.map[node.name];\n        if (this.parser) {\n          this.parser.parseOpen(node);\n          return true;\n        }\n        throw new Error(`Unexpected xml node in parseOpen: ${JSON.stringify(node)}`);\n    }\n  }\n\n  parseText(text) {\n    if (this.parser) {\n      this.parser.parseText(text);\n    }\n  }\n\n  parseClose(name) {\n    if (this.parser) {\n      if (!this.parser.parseClose(name)) {\n        this.model.push(this.parser.model);\n        this.parser = undefined;\n      }\n      return true;\n    }\n    switch (name) {\n      case 'Relationships':\n        return false;\n      default:\n        throw new Error(`Unexpected xml node in parseClose: ${name}`);\n    }\n  }\n}\n\nRelationshipsXform.RELATIONSHIPS_ATTRIBUTES = {\n  xmlns: 'http://schemas.openxmlformats.org/package/2006/relationships',\n};\n\nmodule.exports = RelationshipsXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,2BAA2B,CAAC;AACtD,MAAMC,SAAS,GAAGD,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAME,iBAAiB,GAAGF,OAAO,CAAC,sBAAsB,CAAC;AAEzD,MAAMG,kBAAkB,SAASF,SAAS,CAAC;EACzCG,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,GAAG,GAAG;MACTC,YAAY,EAAE,IAAIJ,iBAAiB,CAAC;IACtC,CAAC;EACH;EAEAK,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvBA,KAAK,GAAGA,KAAK,IAAI,IAAI,CAACC,OAAO;IAC7BF,SAAS,CAACG,OAAO,CAACZ,SAAS,CAACa,gBAAgB,CAAC;IAC7CJ,SAAS,CAACK,QAAQ,CAAC,eAAe,EAAEV,kBAAkB,CAACW,wBAAwB,CAAC;IAEhFL,KAAK,CAACM,OAAO,CAACC,YAAY,IAAI;MAC5B,IAAI,CAACX,GAAG,CAACC,YAAY,CAACC,MAAM,CAACC,SAAS,EAAEQ,YAAY,CAAC;IACvD,CAAC,CAAC;IAEFR,SAAS,CAACS,SAAS,CAAC,CAAC;EACvB;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACC,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;MAC3B,OAAO,IAAI;IACb;IACA,QAAQA,IAAI,CAACE,IAAI;MACf,KAAK,eAAe;QAClB,IAAI,CAACZ,KAAK,GAAG,EAAE;QACf,OAAO,IAAI;MACb;QACE,IAAI,CAACW,MAAM,GAAG,IAAI,CAACf,GAAG,CAACc,IAAI,CAACE,IAAI,CAAC;QACjC,IAAI,IAAI,CAACD,MAAM,EAAE;UACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;UAC3B,OAAO,IAAI;QACb;QACA,MAAM,IAAIG,KAAK,CAAE,qCAAoCC,IAAI,CAACC,SAAS,CAACL,IAAI,CAAE,EAAC,CAAC;IAChF;EACF;EAEAM,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACN,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACK,SAAS,CAACC,IAAI,CAAC;IAC7B;EACF;EAEAC,UAAUA,CAACN,IAAI,EAAE;IACf,IAAI,IAAI,CAACD,MAAM,EAAE;MACf,IAAI,CAAC,IAAI,CAACA,MAAM,CAACO,UAAU,CAACN,IAAI,CAAC,EAAE;QACjC,IAAI,CAACZ,KAAK,CAACmB,IAAI,CAAC,IAAI,CAACR,MAAM,CAACX,KAAK,CAAC;QAClC,IAAI,CAACW,MAAM,GAAGS,SAAS;MACzB;MACA,OAAO,IAAI;IACb;IACA,QAAQR,IAAI;MACV,KAAK,eAAe;QAClB,OAAO,KAAK;MACd;QACE,MAAM,IAAIC,KAAK,CAAE,sCAAqCD,IAAK,EAAC,CAAC;IACjE;EACF;AACF;AAEAlB,kBAAkB,CAACW,wBAAwB,GAAG;EAC5CgB,KAAK,EAAE;AACT,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAG7B,kBAAkB"}