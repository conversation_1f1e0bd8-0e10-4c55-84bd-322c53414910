{"version": 3, "file": "nv-pic-pr-xform.js", "names": ["BaseXform", "require", "CNvPrXform", "CNvPicPrXform", "NvPicPrXform", "constructor", "map", "tag", "render", "xmlStream", "model", "openNode", "closeNode", "parseOpen", "node", "parser", "name", "reset", "parseText", "parseClose", "undefined", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/drawing/nv-pic-pr-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\nconst CNvPrXform = require('./c-nv-pr-xform');\nconst CNvPicPrXform = require('./c-nv-pic-pr-xform');\n\nclass NvPicPrXform extends BaseXform {\n  constructor() {\n    super();\n\n    this.map = {\n      'xdr:cNvPr': new CNvPrXform(),\n      'xdr:cNvPicPr': new CNvPicPrXform(),\n    };\n  }\n\n  get tag() {\n    return 'xdr:nvPicPr';\n  }\n\n  render(xmlStream, model) {\n    xmlStream.openNode(this.tag);\n    this.map['xdr:cNvPr'].render(xmlStream, model);\n    this.map['xdr:cNvPicPr'].render(xmlStream, model);\n    xmlStream.closeNode();\n  }\n\n  parseOpen(node) {\n    if (this.parser) {\n      this.parser.parseOpen(node);\n      return true;\n    }\n\n    switch (node.name) {\n      case this.tag:\n        this.reset();\n        break;\n      default:\n        this.parser = this.map[node.name];\n        if (this.parser) {\n          this.parser.parseOpen(node);\n        }\n        break;\n    }\n    return true;\n  }\n\n  parseText() {}\n\n  parseClose(name) {\n    if (this.parser) {\n      if (!this.parser.parseClose(name)) {\n        this.parser = undefined;\n      }\n      return true;\n    }\n    switch (name) {\n      case this.tag:\n        this.model = this.map['xdr:cNvPr'].model;\n        return false;\n      default:\n        return true;\n    }\n  }\n}\n\nmodule.exports = NvPicPrXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;AAC1C,MAAMC,UAAU,GAAGD,OAAO,CAAC,iBAAiB,CAAC;AAC7C,MAAME,aAAa,GAAGF,OAAO,CAAC,qBAAqB,CAAC;AAEpD,MAAMG,YAAY,SAASJ,SAAS,CAAC;EACnCK,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,GAAG,GAAG;MACT,WAAW,EAAE,IAAIJ,UAAU,CAAC,CAAC;MAC7B,cAAc,EAAE,IAAIC,aAAa,CAAC;IACpC,CAAC;EACH;EAEA,IAAII,GAAGA,CAAA,EAAG;IACR,OAAO,aAAa;EACtB;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvBD,SAAS,CAACE,QAAQ,CAAC,IAAI,CAACJ,GAAG,CAAC;IAC5B,IAAI,CAACD,GAAG,CAAC,WAAW,CAAC,CAACE,MAAM,CAACC,SAAS,EAAEC,KAAK,CAAC;IAC9C,IAAI,CAACJ,GAAG,CAAC,cAAc,CAAC,CAACE,MAAM,CAACC,SAAS,EAAEC,KAAK,CAAC;IACjDD,SAAS,CAACG,SAAS,CAAC,CAAC;EACvB;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACC,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;MAC3B,OAAO,IAAI;IACb;IAEA,QAAQA,IAAI,CAACE,IAAI;MACf,KAAK,IAAI,CAACT,GAAG;QACX,IAAI,CAACU,KAAK,CAAC,CAAC;QACZ;MACF;QACE,IAAI,CAACF,MAAM,GAAG,IAAI,CAACT,GAAG,CAACQ,IAAI,CAACE,IAAI,CAAC;QACjC,IAAI,IAAI,CAACD,MAAM,EAAE;UACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;QAC7B;QACA;IACJ;IACA,OAAO,IAAI;EACb;EAEAI,SAASA,CAAA,EAAG,CAAC;EAEbC,UAAUA,CAACH,IAAI,EAAE;IACf,IAAI,IAAI,CAACD,MAAM,EAAE;MACf,IAAI,CAAC,IAAI,CAACA,MAAM,CAACI,UAAU,CAACH,IAAI,CAAC,EAAE;QACjC,IAAI,CAACD,MAAM,GAAGK,SAAS;MACzB;MACA,OAAO,IAAI;IACb;IACA,QAAQJ,IAAI;MACV,KAAK,IAAI,CAACT,GAAG;QACX,IAAI,CAACG,KAAK,GAAG,IAAI,CAACJ,GAAG,CAAC,WAAW,CAAC,CAACI,KAAK;QACxC,OAAO,KAAK;MACd;QACE,OAAO,IAAI;IACf;EACF;AACF;AAEAW,MAAM,CAACC,OAAO,GAAGlB,YAAY"}