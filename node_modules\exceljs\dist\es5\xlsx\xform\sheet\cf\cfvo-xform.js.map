{"version": 3, "file": "cfvo-xform.js", "names": ["BaseXform", "require", "CfvoXform", "tag", "render", "xmlStream", "model", "leafNode", "type", "val", "value", "parseOpen", "node", "attributes", "toFloatValue", "parseClose", "name", "module", "exports"], "sources": ["../../../../../../lib/xlsx/xform/sheet/cf/cfvo-xform.js"], "sourcesContent": ["const BaseXform = require('../../base-xform');\n\nclass CfvoXform extends BaseXform {\n  get tag() {\n    return 'cfvo';\n  }\n\n  render(xmlStream, model) {\n    xmlStream.leafNode(this.tag, {\n      type: model.type,\n      val: model.value,\n    });\n  }\n\n  parseOpen(node) {\n    this.model = {\n      type: node.attributes.type,\n      value: BaseXform.toFloatValue(node.attributes.val),\n    };\n  }\n\n  parseClose(name) {\n    return name !== this.tag;\n  }\n}\n\nmodule.exports = CfvoXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AAE7C,MAAMC,SAAS,SAASF,SAAS,CAAC;EAChC,IAAIG,GAAGA,CAAA,EAAG;IACR,OAAO,MAAM;EACf;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvBD,SAAS,CAACE,QAAQ,CAAC,IAAI,CAACJ,GAAG,EAAE;MAC3BK,IAAI,EAAEF,KAAK,CAACE,IAAI;MAChBC,GAAG,EAAEH,KAAK,CAACI;IACb,CAAC,CAAC;EACJ;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,CAACN,KAAK,GAAG;MACXE,IAAI,EAAEI,IAAI,CAACC,UAAU,CAACL,IAAI;MAC1BE,KAAK,EAAEV,SAAS,CAACc,YAAY,CAACF,IAAI,CAACC,UAAU,CAACJ,GAAG;IACnD,CAAC;EACH;EAEAM,UAAUA,CAACC,IAAI,EAAE;IACf,OAAOA,IAAI,KAAK,IAAI,CAACb,GAAG;EAC1B;AACF;AAEAc,MAAM,CAACC,OAAO,GAAGhB,SAAS"}