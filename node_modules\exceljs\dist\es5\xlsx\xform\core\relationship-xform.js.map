{"version": 3, "file": "relationship-xform.js", "names": ["BaseXform", "require", "RelationshipXform", "render", "xmlStream", "model", "leafNode", "parseOpen", "node", "name", "attributes", "parseText", "parseClose", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/core/relationship-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\n\nclass RelationshipXform extends BaseXform {\n  render(xmlStream, model) {\n    xmlStream.leafNode('Relationship', model);\n  }\n\n  parseOpen(node) {\n    switch (node.name) {\n      case 'Relationship':\n        this.model = node.attributes;\n        return true;\n      default:\n        return false;\n    }\n  }\n\n  parseText() {}\n\n  parseClose() {\n    return false;\n  }\n}\n\nmodule.exports = RelationshipXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAMC,iBAAiB,SAASF,SAAS,CAAC;EACxCG,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvBD,SAAS,CAACE,QAAQ,CAAC,cAAc,EAAED,KAAK,CAAC;EAC3C;EAEAE,SAASA,CAACC,IAAI,EAAE;IACd,QAAQA,IAAI,CAACC,IAAI;MACf,KAAK,cAAc;QACjB,IAAI,CAACJ,KAAK,GAAGG,IAAI,CAACE,UAAU;QAC5B,OAAO,IAAI;MACb;QACE,OAAO,KAAK;IAChB;EACF;EAEAC,SAASA,CAAA,EAAG,CAAC;EAEbC,UAAUA,CAAA,EAAG;IACX,OAAO,KAAK;EACd;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGZ,iBAAiB"}