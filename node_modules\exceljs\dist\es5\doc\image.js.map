{"version": 3, "file": "image.js", "names": ["co<PERSON><PERSON><PERSON>", "require", "<PERSON><PERSON>", "Image", "constructor", "worksheet", "model", "type", "imageId", "hyperlinks", "range", "tl", "br", "ext", "editAs", "Error", "_ref", "decoded", "decode", "col", "left", "row", "top", "right", "bottom", "module", "exports"], "sources": ["../../../lib/doc/image.js"], "sourcesContent": ["const colCache = require('../utils/col-cache');\nconst Anchor = require('./anchor');\n\nclass Image {\n  constructor(worksheet, model) {\n    this.worksheet = worksheet;\n    this.model = model;\n  }\n\n  get model() {\n    switch (this.type) {\n      case 'background':\n        return {\n          type: this.type,\n          imageId: this.imageId,\n        };\n      case 'image':\n        return {\n          type: this.type,\n          imageId: this.imageId,\n          hyperlinks: this.range.hyperlinks,\n          range: {\n            tl: this.range.tl.model,\n            br: this.range.br && this.range.br.model,\n            ext: this.range.ext,\n            editAs: this.range.editAs,\n          },\n        };\n      default:\n        throw new Error('Invalid Image Type');\n    }\n  }\n\n  set model({type, imageId, range, hyperlinks}) {\n    this.type = type;\n    this.imageId = imageId;\n\n    if (type === 'image') {\n      if (typeof range === 'string') {\n        const decoded = colCache.decode(range);\n        this.range = {\n          tl: new Anchor(this.worksheet, {col: decoded.left, row: decoded.top}, -1),\n          br: new Anchor(this.worksheet, {col: decoded.right, row: decoded.bottom}, 0),\n          editAs: 'oneCell',\n        };\n      } else {\n        this.range = {\n          tl: new Anchor(this.worksheet, range.tl, 0),\n          br: range.br && new Anchor(this.worksheet, range.br, 0),\n          ext: range.ext,\n          editAs: range.editAs,\n          hyperlinks: hyperlinks || range.hyperlinks,\n        };\n      }\n    }\n  }\n}\n\nmodule.exports = Image;\n"], "mappings": ";;AAAA,MAAMA,QAAQ,GAAGC,OAAO,CAAC,oBAAoB,CAAC;AAC9C,MAAMC,MAAM,GAAGD,OAAO,CAAC,UAAU,CAAC;AAElC,MAAME,KAAK,CAAC;EACVC,WAAWA,CAACC,SAAS,EAAEC,KAAK,EAAE;IAC5B,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,KAAK,GAAGA,KAAK;EACpB;EAEA,IAAIA,KAAKA,CAAA,EAAG;IACV,QAAQ,IAAI,CAACC,IAAI;MACf,KAAK,YAAY;QACf,OAAO;UACLA,IAAI,EAAE,IAAI,CAACA,IAAI;UACfC,OAAO,EAAE,IAAI,CAACA;QAChB,CAAC;MACH,KAAK,OAAO;QACV,OAAO;UACLD,IAAI,EAAE,IAAI,CAACA,IAAI;UACfC,OAAO,EAAE,IAAI,CAACA,OAAO;UACrBC,UAAU,EAAE,IAAI,CAACC,KAAK,CAACD,UAAU;UACjCC,KAAK,EAAE;YACLC,EAAE,EAAE,IAAI,CAACD,KAAK,CAACC,EAAE,CAACL,KAAK;YACvBM,EAAE,EAAE,IAAI,CAACF,KAAK,CAACE,EAAE,IAAI,IAAI,CAACF,KAAK,CAACE,EAAE,CAACN,KAAK;YACxCO,GAAG,EAAE,IAAI,CAACH,KAAK,CAACG,GAAG;YACnBC,MAAM,EAAE,IAAI,CAACJ,KAAK,CAACI;UACrB;QACF,CAAC;MACH;QACE,MAAM,IAAIC,KAAK,CAAC,oBAAoB,CAAC;IACzC;EACF;EAEA,IAAIT,KAAKA,CAAAU,IAAA,EAAqC;IAAA,IAApC;MAACT,IAAI;MAAEC,OAAO;MAAEE,KAAK;MAAED;IAAU,CAAC,GAAAO,IAAA;IAC1C,IAAI,CAACT,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,OAAO,GAAGA,OAAO;IAEtB,IAAID,IAAI,KAAK,OAAO,EAAE;MACpB,IAAI,OAAOG,KAAK,KAAK,QAAQ,EAAE;QAC7B,MAAMO,OAAO,GAAGjB,QAAQ,CAACkB,MAAM,CAACR,KAAK,CAAC;QACtC,IAAI,CAACA,KAAK,GAAG;UACXC,EAAE,EAAE,IAAIT,MAAM,CAAC,IAAI,CAACG,SAAS,EAAE;YAACc,GAAG,EAAEF,OAAO,CAACG,IAAI;YAAEC,GAAG,EAAEJ,OAAO,CAACK;UAAG,CAAC,EAAE,CAAC,CAAC,CAAC;UACzEV,EAAE,EAAE,IAAIV,MAAM,CAAC,IAAI,CAACG,SAAS,EAAE;YAACc,GAAG,EAAEF,OAAO,CAACM,KAAK;YAAEF,GAAG,EAAEJ,OAAO,CAACO;UAAM,CAAC,EAAE,CAAC,CAAC;UAC5EV,MAAM,EAAE;QACV,CAAC;MACH,CAAC,MAAM;QACL,IAAI,CAACJ,KAAK,GAAG;UACXC,EAAE,EAAE,IAAIT,MAAM,CAAC,IAAI,CAACG,SAAS,EAAEK,KAAK,CAACC,EAAE,EAAE,CAAC,CAAC;UAC3CC,EAAE,EAAEF,KAAK,CAACE,EAAE,IAAI,IAAIV,MAAM,CAAC,IAAI,CAACG,SAAS,EAAEK,KAAK,CAACE,EAAE,EAAE,CAAC,CAAC;UACvDC,GAAG,EAAEH,KAAK,CAACG,GAAG;UACdC,MAAM,EAAEJ,KAAK,CAACI,MAAM;UACpBL,UAAU,EAAEA,UAAU,IAAIC,KAAK,CAACD;QAClC,CAAC;MACH;IACF;EACF;AACF;AAEAgB,MAAM,CAACC,OAAO,GAAGvB,KAAK"}