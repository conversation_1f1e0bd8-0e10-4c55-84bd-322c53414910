{"version": 3, "file": "ext-lst-ref-xform.js", "names": ["BaseXform", "require", "CompositeXform", "X14IdXform", "tag", "render", "xmlStream", "model", "leafNode", "parseOpen", "parseText", "text", "parseClose", "name", "ExtXform", "constructor", "map", "idXform", "openNode", "uri", "x14Id", "closeNode", "createNewModel", "onParserClose", "parser", "ExtLstRefXform", "ext", "Object", "assign", "module", "exports"], "sources": ["../../../../../../lib/xlsx/xform/sheet/cf/ext-lst-ref-xform.js"], "sourcesContent": ["/* eslint-disable max-classes-per-file */\nconst BaseXform = require('../../base-xform');\nconst CompositeXform = require('../../composite-xform');\n\nclass X14IdXform extends BaseXform {\n  get tag() {\n    return 'x14:id';\n  }\n\n  render(xmlStream, model) {\n    xmlStream.leafNode(this.tag, null, model);\n  }\n\n  parseOpen() {\n    this.model = '';\n  }\n\n  parseText(text) {\n    this.model += text;\n  }\n\n  parseClose(name) {\n    return name !== this.tag;\n  }\n}\n\nclass ExtXform extends CompositeXform {\n  constructor() {\n    super();\n\n    this.map = {\n      'x14:id': (this.idXform = new X14IdXform()),\n    };\n  }\n\n  get tag() {\n    return 'ext';\n  }\n\n  render(xmlStream, model) {\n    xmlStream.openNode(this.tag, {\n      uri: '{B025F937-C7B1-47D3-B67F-A62EFF666E3E}',\n      'xmlns:x14': 'http://schemas.microsoft.com/office/spreadsheetml/2009/9/main',\n    });\n\n    this.idXform.render(xmlStream, model.x14Id);\n\n    xmlStream.closeNode();\n  }\n\n  createNewModel() {\n    return {};\n  }\n\n  onParserClose(name, parser) {\n    this.model.x14Id = parser.model;\n  }\n}\n\nclass ExtLstRefXform extends CompositeXform {\n  constructor() {\n    super();\n    this.map = {\n      ext: new ExtXform(),\n    };\n  }\n\n  get tag() {\n    return 'extLst';\n  }\n\n  render(xmlStream, model) {\n    xmlStream.openNode(this.tag);\n    this.map.ext.render(xmlStream, model);\n    xmlStream.closeNode();\n  }\n\n  createNewModel() {\n    return {};\n  }\n\n  onParserClose(name, parser) {\n    Object.assign(this.model, parser.model);\n  }\n}\n\nmodule.exports = ExtLstRefXform;\n"], "mappings": ";;AAAA;AACA,MAAMA,SAAS,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AAC7C,MAAMC,cAAc,GAAGD,OAAO,CAAC,uBAAuB,CAAC;AAEvD,MAAME,UAAU,SAASH,SAAS,CAAC;EACjC,IAAII,GAAGA,CAAA,EAAG;IACR,OAAO,QAAQ;EACjB;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvBD,SAAS,CAACE,QAAQ,CAAC,IAAI,CAACJ,GAAG,EAAE,IAAI,EAAEG,KAAK,CAAC;EAC3C;EAEAE,SAASA,CAAA,EAAG;IACV,IAAI,CAACF,KAAK,GAAG,EAAE;EACjB;EAEAG,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,CAACJ,KAAK,IAAII,IAAI;EACpB;EAEAC,UAAUA,CAACC,IAAI,EAAE;IACf,OAAOA,IAAI,KAAK,IAAI,CAACT,GAAG;EAC1B;AACF;AAEA,MAAMU,QAAQ,SAASZ,cAAc,CAAC;EACpCa,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,GAAG,GAAG;MACT,QAAQ,EAAG,IAAI,CAACC,OAAO,GAAG,IAAId,UAAU,CAAC;IAC3C,CAAC;EACH;EAEA,IAAIC,GAAGA,CAAA,EAAG;IACR,OAAO,KAAK;EACd;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvBD,SAAS,CAACY,QAAQ,CAAC,IAAI,CAACd,GAAG,EAAE;MAC3Be,GAAG,EAAE,wCAAwC;MAC7C,WAAW,EAAE;IACf,CAAC,CAAC;IAEF,IAAI,CAACF,OAAO,CAACZ,MAAM,CAACC,SAAS,EAAEC,KAAK,CAACa,KAAK,CAAC;IAE3Cd,SAAS,CAACe,SAAS,CAAC,CAAC;EACvB;EAEAC,cAAcA,CAAA,EAAG;IACf,OAAO,CAAC,CAAC;EACX;EAEAC,aAAaA,CAACV,IAAI,EAAEW,MAAM,EAAE;IAC1B,IAAI,CAACjB,KAAK,CAACa,KAAK,GAAGI,MAAM,CAACjB,KAAK;EACjC;AACF;AAEA,MAAMkB,cAAc,SAASvB,cAAc,CAAC;EAC1Ca,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,GAAG,GAAG;MACTU,GAAG,EAAE,IAAIZ,QAAQ,CAAC;IACpB,CAAC;EACH;EAEA,IAAIV,GAAGA,CAAA,EAAG;IACR,OAAO,QAAQ;EACjB;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvBD,SAAS,CAACY,QAAQ,CAAC,IAAI,CAACd,GAAG,CAAC;IAC5B,IAAI,CAACY,GAAG,CAACU,GAAG,CAACrB,MAAM,CAACC,SAAS,EAAEC,KAAK,CAAC;IACrCD,SAAS,CAACe,SAAS,CAAC,CAAC;EACvB;EAEAC,cAAcA,CAAA,EAAG;IACf,OAAO,CAAC,CAAC;EACX;EAEAC,aAAaA,CAACV,IAAI,EAAEW,MAAM,EAAE;IAC1BG,MAAM,CAACC,MAAM,CAAC,IAAI,CAACrB,KAAK,EAAEiB,MAAM,CAACjB,KAAK,CAAC;EACzC;AACF;AAEAsB,MAAM,CAACC,OAAO,GAAGL,cAAc"}