# 申请书模板代码文档

## 概述
本文档提取了系统中申请书模板生成功能的完整代码，包括HTML结构、CSS样式、JavaScript逻辑和PDF生成功能。

## 1. HTML 模态框结构

```html
<!-- 申请书模板预览模态框 -->
<div id="applicationTemplateModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex justify-center items-start overflow-y-auto hidden z-50 p-4">
    <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full relative">
        <div class="p-4 border-b">
            <h2 class="text-xl font-bold">申请书</h2>
            <button onclick="closeApplicationTemplate()" class="absolute top-4 right-4 text-gray-500 hover:text-gray-700">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <div class="p-4 overflow-auto" style="max-height: 80vh;">
            <div class="a4-page" id="applicationTemplatePage">
                <div id="applicationTemplateContent"></div>
            </div>
        </div>
        <div class="p-4 border-t flex justify-end space-x-2 template-actions">
            <button onclick="downloadApplicationTemplate()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                下载
            </button>
            <button onclick="closeApplicationTemplate()" class="bg-gray-300 hover:bg-gray-400 px-4 py-2 rounded">
                关闭
            </button>
        </div>
    </div>
</div>
```

## 2. 申请书表格模板

```html
<table class="application-template ${mobileClass}">
    <tr>
        <th colspan="4" class="title">申 请 书</th>
    </tr>
    <tr style="height: 5%;">
        <td class="label">申请部门</td>
        <td>${sanitizeInput(application.department)}</td>
        <td class="label">申请日期</td>
        <td>${formattedDate}</td>
    </tr>
    <tr>
        <td class="label" rowspan="2">申请事由</td>
        <td colspan="3" class="content">${sanitizeInput(application.content).replace(/\n/g, '<br>')}</td>
    </tr>
    <tr style="height: 5%;">
        <td colspan="3" class="applicant">申请人: ${sanitizeInput(application.applicant)}</td>
    </tr>
    <tr>
        <td class="label">厂长意见</td>
        <td colspan="3" class="approval">${factoryApproval}</td>
    </tr>
    <tr>
        <td class="label">总监意见</td>
        <td colspan="3" class="approval">${directorApproval}</td>
    </tr>
    <tr>
        <td class="label">经理核准</td>
        <td colspan="3" class="approval">${managerApproval}</td>
    </tr>
</table>
```

## 3. CSS 样式

```css
/* 申请书模板样式 */
.application-template {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid #000;
    font-family: SimSun, serif;
    height: 100%;
}

.application-template.mobile {
    font-size: 14px;
}

.application-template.mobile-small {
    font-size: 12px;
}

.application-template th, .application-template td {
    border: 1px solid #000;
    padding: 8px;
    text-align: center;
    vertical-align: middle;
}

.application-template .title {
    text-align: center;
    font-size: 24px;
    font-weight: bold;
    padding: 15px;
    height: 10%;
}

.application-template .label {
    width: 15%;
    font-weight: bold;
    text-align: center;
    vertical-align: middle;
}

.application-template .content {
    height: 200px;
    vertical-align: top;
    text-align: left;
    padding: 10px;
}

.application-template .applicant {
    text-align: right;
    padding-right: 50px;
    height: 5%;
}

.application-template .approval {
    min-height: 100px;
    vertical-align: top;
    height: 15%;
}

/* A4纸张样式 */
.a4-page {
    width: 210mm;
    height: 297mm;
    margin: 0 auto;
    background: white;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    padding: 10mm;
    box-sizing: border-box;
}

/* 签名相关样式 */
.signature-image {
    max-width: 120px;
    max-height: 60px;
    margin-bottom: 5px;
}

.approval-date {
    font-size: 12px;
    color: #333;
    text-align: center;
    font-weight: 500;
}

.no-signature {
    color: #666;
    font-style: italic;
    font-size: 12px;
    margin-bottom: 5px;
    font-weight: 500;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .a4-page {
        width: 100%;
        height: auto;
        padding: 10mm;
        transform-origin: top center;
        transform: scale(0.9);
        margin: 0 auto;
        box-sizing: border-box;
        aspect-ratio: 1 / 1.414; /* A4纸张比例 */
    }

    .application-template {
        width: 100%;
        height: 100%;
        font-size: 14px;
        table-layout: fixed;
    }

    .application-template .title {
        font-size: 20px;
        padding: 10px;
        height: 10%;
    }

    .application-template .content {
        height: 40%;
        min-height: 180px;
    }

    .application-template .approval {
        min-height: 90px;
        height: 15%;
    }

    .signature-image {
        max-width: 100px;
        max-height: 50px;
    }
}

@media (max-width: 480px) {
    .a4-page {
        transform: scale(0.7);
        padding: 8mm;
        aspect-ratio: 1 / 1.414;
    }

    .application-template {
        font-size: 12px;
    }

    .application-template .title {
        font-size: 18px;
        padding: 8px;
    }

    .application-template .content {
        height: 40%;
        min-height: 160px;
    }

    .application-template .approval {
        min-height: 80px;
        height: 15%;
    }

    .signature-image {
        max-width: 80px;
        max-height: 40px;
    }
}

/* 打印样式 */
@media print {
    @page {
        size: A4;
        margin: 0;
    }

    body * {
        visibility: hidden;
    }

    #applicationTemplateModal, #applicationTemplateModal * {
        visibility: visible;
    }

    #applicationTemplateModal {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background: white;
        display: flex;
        justify-content: center;
        align-items: flex-start;
        overflow: visible;
    }

    .a4-page {
        box-shadow: none;
        margin: 0;
        padding: 10mm;
        width: 210mm;
        height: 297mm;
        overflow: hidden;
        page-break-after: always;
    }

    .application-template {
        page-break-inside: avoid;
        width: 100%;
        height: 100%;
        border-collapse: collapse;
    }

    .print-hint, .template-actions {
        display: none !important;
    }
}
```

## 4. JavaScript 核心函数

### 4.1 生成申请书模板主函数

```javascript
// 生成申请书模板
function generateApplicationTemplate(application) {
    // 格式化日期
    const formattedDate = formatDate(application.date);

    // 获取审批信息
    let factoryApproval = '';
    let directorApproval = '';
    let managerApproval = '';

    // 处理厂长审批信息
    if (application.approvals && application.approvals.directors) {
        const directorsEntries = Object.entries(application.approvals.directors);
        if (directorsEntries.length > 0) {
            factoryApproval = `<div class="approval-container">`;
            directorsEntries.forEach(([username, approval]) => {
                if (approval.status === 'approved' || approval.status === 'rejected') {
                    factoryApproval += `
                        <div class="single-approval">
                            ${approval.comment ? `<div class="approval-comment">${sanitizeInput(approval.comment)}</div>` : ''}
                            <div class="signature-wrapper">
                                ${approval.signature ?
                                    `<img src="${approval.signature}" alt="${approval.approverName || username}的签名" class="signature-image" style="image-rendering: -webkit-optimize-contrast; filter: contrast(1.05);">` :
                                    `<div class="no-signature">无签名</div>`
                                }
                                <div class="approval-date">${formatDate(approval.date)}</div>
                            </div>
                        </div>
                    `;
                }
            });
            factoryApproval += `</div>`;
        }
    }

    // 处理总监审批信息
    if (application.approvals && application.approvals.chief) {
        const chiefApproval = application.approvals.chief;
        if (chiefApproval.status === 'approved' || chiefApproval.status === 'rejected') {
            directorApproval = `
                <div class="approval-container">
                    <div class="single-approval">
                        ${chiefApproval.comment ? `<div class="approval-comment">${sanitizeInput(chiefApproval.comment)}</div>` : ''}
                        <div class="signature-wrapper">
                            ${chiefApproval.signature ?
                                `<img src="${chiefApproval.signature}" alt="${chiefApproval.approverName || '总监'}的签名" class="signature-image" style="image-rendering: -webkit-optimize-contrast; filter: contrast(1.05);">` :
                                `<div class="no-signature">无签名</div>`
                            }
                            <div class="approval-date">${formatDate(chiefApproval.date)}</div>
                        </div>
                    </div>
                </div>
            `;
        }
    }

    // 处理经理审批信息（包含CEO签名）
    if (application.approvals && application.approvals.managers) {
        const managersEntries = Object.entries(application.approvals.managers);
        if (managersEntries.length > 0) {
            managerApproval = `<div class="approval-container">`;
            managersEntries.forEach(([username, approval]) => {
                if (approval.status === 'approved' || approval.status === 'rejected') {
                    managerApproval += `
                        <div class="single-approval">
                            ${approval.comment ? `<div class="approval-comment">${sanitizeInput(approval.comment)}</div>` : ''}
                            <div class="signature-wrapper">
                                ${approval.signature ?
                                    `<img src="${approval.signature}" alt="${approval.approverName || username}的签名" class="signature-image" style="image-rendering: -webkit-optimize-contrast; filter: contrast(1.05);">` :
                                    `<div class="no-signature">无签名</div>`
                                }
                                <div class="approval-date">${formatDate(approval.date)}</div>
                            </div>
                        </div>
                    `;
                }
            });

            // 添加CEO审批信息到经理核准区域
            if (application.approvals.ceo && (application.approvals.ceo.status === 'approved' || application.approvals.ceo.status === 'rejected')) {
                const ceoApproval = application.approvals.ceo;
                const ceoSignature = ceoApproval.signature;

                managerApproval += `
                    <div class="single-approval">
                        ${ceoApproval.comment ? `<div class="approval-comment">${sanitizeInput(ceoApproval.comment)}</div>` : ''}
                        <div class="signature-wrapper">
                            ${ceoSignature ?
                                `<img src="${ceoSignature}" alt="CEO的签名" class="signature-image" style="image-rendering: -webkit-optimize-contrast; filter: contrast(1.05);">` :
                                `<div class="no-signature">无签名</div>`
                            }
                            <div class="approval-date">${formatDate(ceoApproval.date)}</div>
                        </div>
                    </div>
                `;
            }

            managerApproval += `</div>`;
        }
    }

    // 检测是否为移动设备
    const isMobile = window.innerWidth <= 768;
    const isSmallMobile = window.innerWidth <= 480;

    // 根据设备类型调整样式
    let mobileClass = '';
    if (isSmallMobile) {
        mobileClass = 'mobile-small';
    } else if (isMobile) {
        mobileClass = 'mobile';
    }

    const templateHTML = `
        <table class="application-template ${mobileClass}">
            <tr>
                <th colspan="4" class="title">申 请 书</th>
            </tr>
            <tr style="height: 5%;">
                <td class="label">申请部门</td>
                <td>${sanitizeInput(application.department)}</td>
                <td class="label">申请日期</td>
                <td>${formattedDate}</td>
            </tr>
            <tr>
                <td class="label" rowspan="2">申请事由</td>
                <td colspan="3" class="content">${sanitizeInput(application.content).replace(/\n/g, '<br>')}</td>
            </tr>
            <tr style="height: 5%;">
                <td colspan="3" class="applicant">申请人: ${sanitizeInput(application.applicant)}</td>
            </tr>
            <tr>
                <td class="label">厂长意见</td>
                <td colspan="3" class="approval">${factoryApproval}</td>
            </tr>
            <tr>
                <td class="label">总监意见</td>
                <td colspan="3" class="approval">${directorApproval}</td>
            </tr>
            <tr>
                <td class="label">经理核准</td>
                <td colspan="3" class="approval">${managerApproval}</td>
            </tr>
        </table>
    `;

    // 更新模板内容
    document.getElementById('applicationTemplateContent').innerHTML = templateHTML;

    // 显示模态框
    document.getElementById('applicationTemplateModal').classList.remove('hidden');

    // 调整表格高度以填满A4页面
    adjustTableHeight();
}
```

### 4.2 辅助函数

```javascript
// 格式化日期 (YYYY-MM-DD 转为 YYYY年MM月DD日)
function formatDate(dateString) {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return `${year}年${month}月${day}日`;
}

// 输入内容安全化
function sanitizeInput(input) {
    if (!input) return '';
    return input.toString()
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#x27;');
}

// 关闭申请书模板
function closeApplicationTemplate() {
    document.getElementById('applicationTemplateModal').classList.add('hidden');
}

// 调整表格高度以填满A4页面
function adjustTableHeight() {
    // 获取A4页面的高度
    const a4Page = document.getElementById('applicationTemplatePage');
    const a4Height = a4Page.clientHeight;

    // 获取表格
    const table = document.querySelector('.application-template');
    if (!table) return;

    // 设置表格高度为A4页面高度的90%
    table.style.height = (a4Height * 0.90) + 'px';

    // 获取内容行和其他行
    const contentRow = table.querySelector('tr:nth-child(3)');
    const otherRows = Array.from(table.querySelectorAll('tr')).filter(row => row !== contentRow);

    // 计算其他行的总高度
    let otherRowsHeight = 0;
    otherRows.forEach(row => {
        otherRowsHeight += row.offsetHeight;
    });

    // 计算内容行应该的高度
    const contentHeight = (a4Height * 0.90) - otherRowsHeight;

    // 设置内容行的高度
    if (contentRow && contentHeight > 100) {
        contentRow.style.height = contentHeight + 'px';
    }
}
```

### 4.3 PDF生成功能

```javascript
// 下载申请书模板为PDF
function downloadApplicationTemplate() {
    const element = document.getElementById('applicationTemplatePage');
    const filename = '申请书_' + new Date().toISOString().slice(0, 10) + '.pdf';

    // 显示加载提示
    const loadingMsg = document.createElement('div');
    loadingMsg.className = 'fixed top-0 left-0 w-full bg-blue-500 text-white text-center py-2 z-50';
    loadingMsg.textContent = '正在生成PDF，请稍候...';
    document.body.appendChild(loadingMsg);

    // 先加载PDF库
    window.loadPdfLibraries().then(() => {
        // 使用html2canvas将元素转换为canvas
        html2canvas(element, {
            scale: 2, // 提高清晰度
            useCORS: true, // 允许加载跨域图片
            logging: false,
            allowTaint: true,
            backgroundColor: '#ffffff'
        }).then(canvas => {
            // 使用jsPDF将canvas转换为PDF
            const { jsPDF } = window.jspdf;
            const pdf = new jsPDF('p', 'mm', 'a4');

            // 计算宽高比例
            const imgData = canvas.toDataURL('image/png');
            const pageWidth = pdf.internal.pageSize.getWidth();
            const pageHeight = pdf.internal.pageSize.getHeight();
            const canvasWidth = canvas.width;
            const canvasHeight = canvas.height;
            const ratio = Math.min(pageWidth / canvasWidth, pageHeight / canvasHeight);
            const imgWidth = canvasWidth * ratio;
            const imgHeight = canvasHeight * ratio;

            // 添加图像到PDF，调整y坐标为-5mm，使内容上移
            pdf.addImage(imgData, 'PNG', 0, -5, imgWidth, imgHeight);

            // 下载PDF
            pdf.save(filename);

            // 移除加载提示
            document.body.removeChild(loadingMsg);
        }).catch(error => {
            console.error('生成PDF失败:', error);
            alert('生成PDF失败，请重试');
            document.body.removeChild(loadingMsg);
        });
    }).catch(error => {
        console.error('加载PDF库失败:', error);
        alert('加载PDF库失败，请检查网络连接后重试');
        document.body.removeChild(loadingMsg);
    });
}

// 加载PDF相关库
window.loadPdfLibraries = function() {
    return new Promise((resolve, reject) => {
        // 检查是否已经加载
        if (window.html2canvas && window.jspdf) {
            resolve();
            return;
        }

        let loadedCount = 0;
        const totalLibs = 2;

        function checkComplete() {
            loadedCount++;
            if (loadedCount === totalLibs) {
                resolve();
            }
        }

        // 加载html2canvas
        if (!window.html2canvas) {
            const script1 = document.createElement('script');
            script1.src = 'js/libs/html2canvas.min.js';
            script1.onload = checkComplete;
            script1.onerror = () => reject(new Error('Failed to load html2canvas'));
            document.head.appendChild(script1);
        } else {
            checkComplete();
        }

        // 加载jsPDF
        if (!window.jspdf) {
            const script2 = document.createElement('script');
            script2.src = 'js/libs/jspdf.umd.min.js';
            script2.onload = checkComplete;
            script2.onerror = () => reject(new Error('Failed to load jsPDF'));
            document.head.appendChild(script2);
        } else {
            checkComplete();
        }
    });
};
```

## 5. 数据结构

### 5.1 申请对象结构

```javascript
const application = {
    applicant: "申请人姓名",
    department: "申请部门",
    date: "2024-01-01", // YYYY-MM-DD格式
    content: "申请事由内容",
    approvals: {
        directors: {
            "director1": {
                status: "approved", // approved, rejected, pending
                comment: "审批意见",
                date: "2024-01-02",
                approverName: "厂长姓名",
                signature: "data:image/png;base64,..." // base64签名图片
            }
        },
        chief: {
            status: "approved",
            comment: "总监意见",
            date: "2024-01-03",
            approverName: "总监姓名",
            signature: "data:image/png;base64,..."
        },
        managers: {
            "manager1": {
                status: "approved",
                comment: "经理意见",
                date: "2024-01-04",
                approverName: "经理姓名",
                signature: "data:image/png;base64,..."
            }
        },
        ceo: {
            status: "approved",
            comment: "CEO意见",
            date: "2024-01-05",
            approverName: "CEO",
            signature: "data:image/png;base64,..."
        }
    }
};
```

### 5.2 审批状态说明

- `approved`: 已批准
- `rejected`: 已拒绝
- `pending`: 待审批

## 6. 使用方法

### 6.1 生成申请书模板

```javascript
// 创建申请对象
const application = {
    applicant: "张三",
    department: "技术部",
    date: "2024-01-01",
    content: "申请购买办公设备",
    approvals: {
        // ... 审批信息
    }
};

// 生成并显示申请书模板
generateApplicationTemplate(application);
```

### 6.2 下载PDF

```javascript
// 在模板显示后，用户点击下载按钮
downloadApplicationTemplate();
```

## 7. 依赖库

需要引入以下JavaScript库：

1. **html2canvas.min.js** - 用于将HTML元素转换为Canvas
2. **jspdf.umd.min.js** - 用于生成PDF文件

```html
<script src="js/libs/html2canvas.min.js"></script>
<script src="js/libs/jspdf.umd.min.js"></script>
```

## 8. 特性说明

### 8.1 响应式设计
- 支持桌面端、平板和手机端显示
- 自动调整字体大小和布局
- A4纸张比例保持

### 8.2 签名支持
- 支持base64格式的签名图片
- 自动调整签名图片大小
- 无签名时显示"无签名"提示

### 8.3 多级审批
- 支持厂长、总监、经理、CEO多级审批
- 每个级别可以有多个审批人
- 显示审批意见和日期

### 8.4 PDF生成
- 高清晰度PDF输出
- 保持A4纸张格式
- 自动文件命名（包含日期）

## 9. 注意事项

1. **安全性**: 所有用户输入都经过`sanitizeInput`函数处理，防止XSS攻击
2. **兼容性**: 支持现代浏览器，需要ES6+支持
3. **性能**: PDF生成可能需要几秒时间，已添加加载提示
4. **移动端**: 在移动设备上会自动缩放以适应屏幕大小

## 10. 完整示例

### 10.1 HTML页面结构

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>申请书模板</title>
    <style>
        /* 这里包含上面的所有CSS样式 */
    </style>
</head>
<body>
    <!-- 申请书模板预览模态框 -->
    <div id="applicationTemplateModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex justify-center items-start overflow-y-auto hidden z-50 p-4">
        <!-- 模态框内容 -->
    </div>

    <!-- 触发按钮 -->
    <button onclick="showApplicationTemplate()">预览申请书</button>

    <script src="js/libs/html2canvas.min.js"></script>
    <script src="js/libs/jspdf.umd.min.js"></script>
    <script>
        // 这里包含上面的所有JavaScript代码

        // 示例：显示申请书模板
        function showApplicationTemplate() {
            const application = {
                applicant: "张三",
                department: "技术部",
                date: "2024-01-01",
                content: "申请购买办公设备用于提升工作效率",
                approvals: {
                    directors: {
                        "director1": {
                            status: "approved",
                            comment: "同意购买",
                            date: "2024-01-02",
                            approverName: "李厂长",
                            signature: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
                        }
                    },
                    chief: {
                        status: "approved",
                        comment: "批准",
                        date: "2024-01-03",
                        approverName: "王总监",
                        signature: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
                    }
                }
            };

            generateApplicationTemplate(application);
        }
    </script>
</body>
</html>
```

### 10.2 集成到现有系统

如果要集成到现有系统中，只需要：

1. 将CSS样式添加到现有样式表
2. 将HTML模态框结构添加到页面
3. 将JavaScript函数添加到现有脚本
4. 确保引入必要的依赖库
5. 根据实际数据结构调用`generateApplicationTemplate()`函数

## 11. 扩展功能

### 11.1 自定义模板
可以通过修改`templateHTML`变量来自定义申请书模板格式。

### 11.2 多语言支持
可以通过配置对象来支持多语言显示。

### 11.3 水印功能
可以在PDF生成时添加水印或背景图片。

### 11.4 批量生成
可以扩展为支持批量生成多个申请书的功能。

---

**文档版本**: 1.0
**最后更新**: 2024年12月
**适用系统**: 申请审批管理系统
