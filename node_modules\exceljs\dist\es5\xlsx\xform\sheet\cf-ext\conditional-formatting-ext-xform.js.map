{"version": 3, "file": "conditional-formatting-ext-xform.js", "names": ["CompositeXform", "require", "SqRefExtXform", "CfRuleExtXform", "ConditionalFormattingExtXform", "constructor", "map", "sqRef", "cfRule", "tag", "prepare", "model", "options", "rules", "for<PERSON>ach", "rule", "render", "xmlStream", "some", "isExt", "openNode", "filter", "ref", "closeNode", "createNewModel", "onParserClose", "name", "parser", "push", "module", "exports"], "sources": ["../../../../../../lib/xlsx/xform/sheet/cf-ext/conditional-formatting-ext-xform.js"], "sourcesContent": ["const CompositeXform = require('../../composite-xform');\n\nconst SqRefExtXform = require('./sqref-ext-xform');\nconst CfRuleExtXform = require('./cf-rule-ext-xform');\n\nclass ConditionalFormattingExtXform extends CompositeXform {\n  constructor() {\n    super();\n\n    this.map = {\n      'xm:sqref': (this.sqRef = new SqRefExtXform()),\n      'x14:cfRule': (this.cfRule = new CfRuleExtXform()),\n    };\n  }\n\n  get tag() {\n    return 'x14:conditionalFormatting';\n  }\n\n  prepare(model, options) {\n    model.rules.forEach(rule => {\n      this.cfRule.prepare(rule, options);\n    });\n  }\n\n  render(xmlStream, model) {\n    if (!model.rules.some(CfRuleExtXform.isExt)) {\n      return;\n    }\n\n    xmlStream.openNode(this.tag, {\n      'xmlns:xm': 'http://schemas.microsoft.com/office/excel/2006/main',\n    });\n\n    model.rules.filter(CfRuleExtXform.isExt).forEach(rule => this.cfRule.render(xmlStream, rule));\n\n    // for some odd reason, Excel needs the <xm:sqref> node to be after the rules\n    this.sqRef.render(xmlStream, model.ref);\n\n    xmlStream.closeNode();\n  }\n\n  createNewModel() {\n    return {\n      rules: [],\n    };\n  }\n\n  onParserClose(name, parser) {\n    switch (name) {\n      case 'xm:sqref':\n        this.model.ref = parser.model;\n        break;\n\n      case 'x14:cfRule':\n        this.model.rules.push(parser.model);\n        break;\n    }\n  }\n}\n\nmodule.exports = ConditionalFormattingExtXform;\n"], "mappings": ";;AAAA,MAAMA,cAAc,GAAGC,OAAO,CAAC,uBAAuB,CAAC;AAEvD,MAAMC,aAAa,GAAGD,OAAO,CAAC,mBAAmB,CAAC;AAClD,MAAME,cAAc,GAAGF,OAAO,CAAC,qBAAqB,CAAC;AAErD,MAAMG,6BAA6B,SAASJ,cAAc,CAAC;EACzDK,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,GAAG,GAAG;MACT,UAAU,EAAG,IAAI,CAACC,KAAK,GAAG,IAAIL,aAAa,CAAC,CAAE;MAC9C,YAAY,EAAG,IAAI,CAACM,MAAM,GAAG,IAAIL,cAAc,CAAC;IAClD,CAAC;EACH;EAEA,IAAIM,GAAGA,CAAA,EAAG;IACR,OAAO,2BAA2B;EACpC;EAEAC,OAAOA,CAACC,KAAK,EAAEC,OAAO,EAAE;IACtBD,KAAK,CAACE,KAAK,CAACC,OAAO,CAACC,IAAI,IAAI;MAC1B,IAAI,CAACP,MAAM,CAACE,OAAO,CAACK,IAAI,EAAEH,OAAO,CAAC;IACpC,CAAC,CAAC;EACJ;EAEAI,MAAMA,CAACC,SAAS,EAAEN,KAAK,EAAE;IACvB,IAAI,CAACA,KAAK,CAACE,KAAK,CAACK,IAAI,CAACf,cAAc,CAACgB,KAAK,CAAC,EAAE;MAC3C;IACF;IAEAF,SAAS,CAACG,QAAQ,CAAC,IAAI,CAACX,GAAG,EAAE;MAC3B,UAAU,EAAE;IACd,CAAC,CAAC;IAEFE,KAAK,CAACE,KAAK,CAACQ,MAAM,CAAClB,cAAc,CAACgB,KAAK,CAAC,CAACL,OAAO,CAACC,IAAI,IAAI,IAAI,CAACP,MAAM,CAACQ,MAAM,CAACC,SAAS,EAAEF,IAAI,CAAC,CAAC;;IAE7F;IACA,IAAI,CAACR,KAAK,CAACS,MAAM,CAACC,SAAS,EAAEN,KAAK,CAACW,GAAG,CAAC;IAEvCL,SAAS,CAACM,SAAS,CAAC,CAAC;EACvB;EAEAC,cAAcA,CAAA,EAAG;IACf,OAAO;MACLX,KAAK,EAAE;IACT,CAAC;EACH;EAEAY,aAAaA,CAACC,IAAI,EAAEC,MAAM,EAAE;IAC1B,QAAQD,IAAI;MACV,KAAK,UAAU;QACb,IAAI,CAACf,KAAK,CAACW,GAAG,GAAGK,MAAM,CAAChB,KAAK;QAC7B;MAEF,KAAK,YAAY;QACf,IAAI,CAACA,KAAK,CAACE,KAAK,CAACe,IAAI,CAACD,MAAM,CAAChB,KAAK,CAAC;QACnC;IACJ;EACF;AACF;AAEAkB,MAAM,CAACC,OAAO,GAAG1B,6BAA6B"}