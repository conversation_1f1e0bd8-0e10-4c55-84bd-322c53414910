{"version": 3, "file": "enums.js", "names": ["module", "exports", "ValueType", "<PERSON><PERSON>", "<PERSON><PERSON>", "Number", "String", "Date", "Hyperlink", "Formula", "SharedString", "RichText", "Boolean", "Error", "FormulaType", "None", "Master", "Shared", "RelationshipType", "OfficeDocument", "Worksheet", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SharedStrings", "Styles", "Theme", "DocumentType", "Xlsx", "ReadingOrder", "LeftToRight", "RightToLeft", "ErrorV<PERSON>ue", "NotApplicable", "Ref", "Name", "DivZero", "Value", "<PERSON><PERSON>"], "sources": ["../../../lib/doc/enums.js"], "sourcesContent": ["'use strict';\n\nmodule.exports = {\n  ValueType: {\n    Null: 0,\n    Merge: 1,\n    Number: 2,\n    String: 3,\n    Date: 4,\n    Hyperlink: 5,\n    Formula: 6,\n    SharedString: 7,\n    RichText: 8,\n    <PERSON>olean: 9,\n    Error: 10,\n  },\n  FormulaType: {\n    None: 0,\n    Master: 1,\n    Shared: 2,\n  },\n  RelationshipType: {\n    None: 0,\n    OfficeDocument: 1,\n    Worksheet: 2,\n    <PERSON><PERSON><PERSON><PERSON><PERSON>: 3,\n    SharedStrings: 4,\n    Styles: 5,\n    Theme: 6,\n    Hyperlink: 7,\n  },\n  DocumentType: {\n    Xlsx: 1,\n  },\n  ReadingOrder: {\n    LeftToRight: 1,\n    RightToLeft: 2,\n  },\n  ErrorValue: {\n    NotApplicable: '#N/A',\n    Ref: '#REF!',\n    Name: '#NAME?',\n    DivZero: '#DIV/0!',\n    Null: '#NULL!',\n    Value: '#VALUE!',\n    Num: '#NUM!',\n  },\n};\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAG;EACfC,SAAS,EAAE;IACTC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTC,MAAM,EAAE,CAAC;IACTC,IAAI,EAAE,CAAC;IACPC,SAAS,EAAE,CAAC;IACZC,OAAO,EAAE,CAAC;IACVC,YAAY,EAAE,CAAC;IACfC,QAAQ,EAAE,CAAC;IACXC,OAAO,EAAE,CAAC;IACVC,KAAK,EAAE;EACT,CAAC;EACDC,WAAW,EAAE;IACXC,IAAI,EAAE,CAAC;IACPC,MAAM,EAAE,CAAC;IACTC,MAAM,EAAE;EACV,CAAC;EACDC,gBAAgB,EAAE;IAChBH,IAAI,EAAE,CAAC;IACPI,cAAc,EAAE,CAAC;IACjBC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE,CAAC;IACZC,aAAa,EAAE,CAAC;IAChBC,MAAM,EAAE,CAAC;IACTC,KAAK,EAAE,CAAC;IACRhB,SAAS,EAAE;EACb,CAAC;EACDiB,YAAY,EAAE;IACZC,IAAI,EAAE;EACR,CAAC;EACDC,YAAY,EAAE;IACZC,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE;EACf,CAAC;EACDC,UAAU,EAAE;IACVC,aAAa,EAAE,MAAM;IACrBC,GAAG,EAAE,OAAO;IACZC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,SAAS;IAClB/B,IAAI,EAAE,QAAQ;IACdgC,KAAK,EAAE,SAAS;IAChBC,GAAG,EAAE;EACP;AACF,CAAC"}