{"version": 3, "file": "base-cell-anchor-xform.js", "names": ["BaseXform", "require", "BaseCellAnchorXform", "parseOpen", "node", "parser", "name", "tag", "reset", "model", "range", "editAs", "attributes", "map", "parseText", "text", "reconcilePicture", "options", "rId", "rel", "rels", "match", "Target", "mediaId", "mediaIndex", "media", "undefined", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/drawing/base-cell-anchor-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\n\nclass BaseCellAnchorXform extends BaseXform {\n  parseOpen(node) {\n    if (this.parser) {\n      this.parser.parseOpen(node);\n      return true;\n    }\n    switch (node.name) {\n      case this.tag:\n        this.reset();\n        this.model = {\n          range: {\n            editAs: node.attributes.editAs || 'oneCell',\n          },\n        };\n        break;\n      default:\n        this.parser = this.map[node.name];\n        if (this.parser) {\n          this.parser.parseOpen(node);\n        }\n        break;\n    }\n    return true;\n  }\n\n  parseText(text) {\n    if (this.parser) {\n      this.parser.parseText(text);\n    }\n  }\n\n  reconcilePicture(model, options) {\n    if (model && model.rId) {\n      const rel = options.rels[model.rId];\n      const match = rel.Target.match(/.*\\/media\\/(.+[.][a-zA-Z]{3,4})/);\n      if (match) {\n        const name = match[1];\n        const mediaId = options.mediaIndex[name];\n        return options.media[mediaId];\n      }\n    }\n    return undefined;\n  }\n}\n\nmodule.exports = BaseCellAnchorXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAMC,mBAAmB,SAASF,SAAS,CAAC;EAC1CG,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACC,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;MAC3B,OAAO,IAAI;IACb;IACA,QAAQA,IAAI,CAACE,IAAI;MACf,KAAK,IAAI,CAACC,GAAG;QACX,IAAI,CAACC,KAAK,CAAC,CAAC;QACZ,IAAI,CAACC,KAAK,GAAG;UACXC,KAAK,EAAE;YACLC,MAAM,EAAEP,IAAI,CAACQ,UAAU,CAACD,MAAM,IAAI;UACpC;QACF,CAAC;QACD;MACF;QACE,IAAI,CAACN,MAAM,GAAG,IAAI,CAACQ,GAAG,CAACT,IAAI,CAACE,IAAI,CAAC;QACjC,IAAI,IAAI,CAACD,MAAM,EAAE;UACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;QAC7B;QACA;IACJ;IACA,OAAO,IAAI;EACb;EAEAU,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACV,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACS,SAAS,CAACC,IAAI,CAAC;IAC7B;EACF;EAEAC,gBAAgBA,CAACP,KAAK,EAAEQ,OAAO,EAAE;IAC/B,IAAIR,KAAK,IAAIA,KAAK,CAACS,GAAG,EAAE;MACtB,MAAMC,GAAG,GAAGF,OAAO,CAACG,IAAI,CAACX,KAAK,CAACS,GAAG,CAAC;MACnC,MAAMG,KAAK,GAAGF,GAAG,CAACG,MAAM,CAACD,KAAK,CAAC,iCAAiC,CAAC;MACjE,IAAIA,KAAK,EAAE;QACT,MAAMf,IAAI,GAAGe,KAAK,CAAC,CAAC,CAAC;QACrB,MAAME,OAAO,GAAGN,OAAO,CAACO,UAAU,CAAClB,IAAI,CAAC;QACxC,OAAOW,OAAO,CAACQ,KAAK,CAACF,OAAO,CAAC;MAC/B;IACF;IACA,OAAOG,SAAS;EAClB;AACF;AAEAC,MAAM,CAACC,OAAO,GAAG1B,mBAAmB"}