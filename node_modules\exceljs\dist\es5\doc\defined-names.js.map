{"version": 3, "file": "defined-names.js", "names": ["_", "require", "co<PERSON><PERSON><PERSON>", "CellMatrix", "Range", "rangeRegexp", "DefinedNames", "constructor", "matrixMap", "getMatrix", "name", "matrix", "add", "locStr", "location", "decodeEx", "addEx", "top", "col", "left", "right", "row", "bottom", "address", "sheetName", "n2l", "addCellEx", "remove", "removeEx", "removeCellEx", "removeAllNames", "each", "for<PERSON>ach", "callback", "cell", "getNames", "addressStr", "getNamesEx", "map", "findCellEx", "filter", "Boolean", "_explore", "mark", "range", "x", "y", "vGrow", "yy", "edge", "c", "findCellAt", "h<PERSON>row", "xx", "cells", "push", "i", "length", "getRanges", "ranges", "$shortRange", "normaliseMatrix", "forEachInSheet", "spliceRows", "start", "numDelete", "numInsert", "spliceColumns", "model", "definedName", "value", "rangeStr", "test", "split", "pop", "addCell", "module", "exports"], "sources": ["../../../lib/doc/defined-names.js"], "sourcesContent": ["'use strict';\n\nconst _ = require('../utils/under-dash');\nconst colCache = require('../utils/col-cache');\nconst CellMatrix = require('../utils/cell-matrix');\nconst Range = require('./range');\n\nconst rangeRegexp = /[$](\\w+)[$](\\d+)(:[$](\\w+)[$](\\d+))?/;\n\nclass DefinedNames {\n  constructor() {\n    this.matrixMap = {};\n  }\n\n  getMatrix(name) {\n    const matrix = this.matrixMap[name] || (this.matrixMap[name] = new CellMatrix());\n    return matrix;\n  }\n\n  // add a name to a cell. locStr in the form SheetName!$col$row or SheetName!$c1$r1:$c2:$r2\n  add(locStr, name) {\n    const location = colCache.decodeEx(locStr);\n    this.addEx(location, name);\n  }\n\n  addEx(location, name) {\n    const matrix = this.getMatrix(name);\n    if (location.top) {\n      for (let col = location.left; col <= location.right; col++) {\n        for (let row = location.top; row <= location.bottom; row++) {\n          const address = {\n            sheetName: location.sheetName,\n            address: colCache.n2l(col) + row,\n            row,\n            col,\n          };\n\n          matrix.addCellEx(address);\n        }\n      }\n    } else {\n      matrix.addCellEx(location);\n    }\n  }\n\n  remove(locStr, name) {\n    const location = colCache.decodeEx(locStr);\n    this.removeEx(location, name);\n  }\n\n  removeEx(location, name) {\n    const matrix = this.getMatrix(name);\n    matrix.removeCellEx(location);\n  }\n\n  removeAllNames(location) {\n    _.each(this.matrixMap, matrix => {\n      matrix.removeCellEx(location);\n    });\n  }\n\n  forEach(callback) {\n    _.each(this.matrixMap, (matrix, name) => {\n      matrix.forEach(cell => {\n        callback(name, cell);\n      });\n    });\n  }\n\n  // get all the names of a cell\n  getNames(addressStr) {\n    return this.getNamesEx(colCache.decodeEx(addressStr));\n  }\n\n  getNamesEx(address) {\n    return _.map(this.matrixMap, (matrix, name) => matrix.findCellEx(address) && name).filter(\n      Boolean\n    );\n  }\n\n  _explore(matrix, cell) {\n    cell.mark = false;\n    const {sheetName} = cell;\n\n    const range = new Range(cell.row, cell.col, cell.row, cell.col, sheetName);\n    let x;\n    let y;\n\n    // grow vertical - only one col to worry about\n    function vGrow(yy, edge) {\n      const c = matrix.findCellAt(sheetName, yy, cell.col);\n      if (!c || !c.mark) {\n        return false;\n      }\n      range[edge] = yy;\n      c.mark = false;\n      return true;\n    }\n    for (y = cell.row - 1; vGrow(y, 'top'); y--);\n    for (y = cell.row + 1; vGrow(y, 'bottom'); y++);\n\n    // grow horizontal - ensure all rows can grow\n    function hGrow(xx, edge) {\n      const cells = [];\n      for (y = range.top; y <= range.bottom; y++) {\n        const c = matrix.findCellAt(sheetName, y, xx);\n        if (c && c.mark) {\n          cells.push(c);\n        } else {\n          return false;\n        }\n      }\n      range[edge] = xx;\n      for (let i = 0; i < cells.length; i++) {\n        cells[i].mark = false;\n      }\n      return true;\n    }\n    for (x = cell.col - 1; hGrow(x, 'left'); x--);\n    for (x = cell.col + 1; hGrow(x, 'right'); x++);\n\n    return range;\n  }\n\n  getRanges(name, matrix) {\n    matrix = matrix || this.matrixMap[name];\n\n    if (!matrix) {\n      return {name, ranges: []};\n    }\n\n    // mark and sweep!\n    matrix.forEach(cell => {\n      cell.mark = true;\n    });\n    const ranges = matrix\n      .map(cell => cell.mark && this._explore(matrix, cell))\n      .filter(Boolean)\n      .map(range => range.$shortRange);\n\n    return {\n      name,\n      ranges,\n    };\n  }\n\n  normaliseMatrix(matrix, sheetName) {\n    // some of the cells might have shifted on specified sheet\n    // need to reassign rows, cols\n    matrix.forEachInSheet(sheetName, (cell, row, col) => {\n      if (cell) {\n        if (cell.row !== row || cell.col !== col) {\n          cell.row = row;\n          cell.col = col;\n          cell.address = colCache.n2l(col) + row;\n        }\n      }\n    });\n  }\n\n  spliceRows(sheetName, start, numDelete, numInsert) {\n    _.each(this.matrixMap, matrix => {\n      matrix.spliceRows(sheetName, start, numDelete, numInsert);\n      this.normaliseMatrix(matrix, sheetName);\n    });\n  }\n\n  spliceColumns(sheetName, start, numDelete, numInsert) {\n    _.each(this.matrixMap, matrix => {\n      matrix.spliceColumns(sheetName, start, numDelete, numInsert);\n      this.normaliseMatrix(matrix, sheetName);\n    });\n  }\n\n  get model() {\n    // To get names per cell - just iterate over all names finding cells if they exist\n    return _.map(this.matrixMap, (matrix, name) => this.getRanges(name, matrix)).filter(\n      definedName => definedName.ranges.length\n    );\n  }\n\n  set model(value) {\n    // value is [ { name, ranges }, ... ]\n    const matrixMap = (this.matrixMap = {});\n    value.forEach(definedName => {\n      const matrix = (matrixMap[definedName.name] = new CellMatrix());\n      definedName.ranges.forEach(rangeStr => {\n        if (rangeRegexp.test(rangeStr.split('!').pop() || '')) {\n          matrix.addCell(rangeStr);\n        }\n      });\n    });\n  }\n}\n\nmodule.exports = DefinedNames;\n"], "mappings": "AAAA,YAAY;;AAEZ,MAAMA,CAAC,GAAGC,OAAO,CAAC,qBAAqB,CAAC;AACxC,MAAMC,QAAQ,GAAGD,OAAO,CAAC,oBAAoB,CAAC;AAC9C,MAAME,UAAU,GAAGF,OAAO,CAAC,sBAAsB,CAAC;AAClD,MAAMG,KAAK,GAAGH,OAAO,CAAC,SAAS,CAAC;AAEhC,MAAMI,WAAW,GAAG,sCAAsC;AAE1D,MAAMC,YAAY,CAAC;EACjBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,SAAS,GAAG,CAAC,CAAC;EACrB;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,MAAMC,MAAM,GAAG,IAAI,CAACH,SAAS,CAACE,IAAI,CAAC,KAAK,IAAI,CAACF,SAAS,CAACE,IAAI,CAAC,GAAG,IAAIP,UAAU,CAAC,CAAC,CAAC;IAChF,OAAOQ,MAAM;EACf;;EAEA;EACAC,GAAGA,CAACC,MAAM,EAAEH,IAAI,EAAE;IAChB,MAAMI,QAAQ,GAAGZ,QAAQ,CAACa,QAAQ,CAACF,MAAM,CAAC;IAC1C,IAAI,CAACG,KAAK,CAACF,QAAQ,EAAEJ,IAAI,CAAC;EAC5B;EAEAM,KAAKA,CAACF,QAAQ,EAAEJ,IAAI,EAAE;IACpB,MAAMC,MAAM,GAAG,IAAI,CAACF,SAAS,CAACC,IAAI,CAAC;IACnC,IAAII,QAAQ,CAACG,GAAG,EAAE;MAChB,KAAK,IAAIC,GAAG,GAAGJ,QAAQ,CAACK,IAAI,EAAED,GAAG,IAAIJ,QAAQ,CAACM,KAAK,EAAEF,GAAG,EAAE,EAAE;QAC1D,KAAK,IAAIG,GAAG,GAAGP,QAAQ,CAACG,GAAG,EAAEI,GAAG,IAAIP,QAAQ,CAACQ,MAAM,EAAED,GAAG,EAAE,EAAE;UAC1D,MAAME,OAAO,GAAG;YACdC,SAAS,EAAEV,QAAQ,CAACU,SAAS;YAC7BD,OAAO,EAAErB,QAAQ,CAACuB,GAAG,CAACP,GAAG,CAAC,GAAGG,GAAG;YAChCA,GAAG;YACHH;UACF,CAAC;UAEDP,MAAM,CAACe,SAAS,CAACH,OAAO,CAAC;QAC3B;MACF;IACF,CAAC,MAAM;MACLZ,MAAM,CAACe,SAAS,CAACZ,QAAQ,CAAC;IAC5B;EACF;EAEAa,MAAMA,CAACd,MAAM,EAAEH,IAAI,EAAE;IACnB,MAAMI,QAAQ,GAAGZ,QAAQ,CAACa,QAAQ,CAACF,MAAM,CAAC;IAC1C,IAAI,CAACe,QAAQ,CAACd,QAAQ,EAAEJ,IAAI,CAAC;EAC/B;EAEAkB,QAAQA,CAACd,QAAQ,EAAEJ,IAAI,EAAE;IACvB,MAAMC,MAAM,GAAG,IAAI,CAACF,SAAS,CAACC,IAAI,CAAC;IACnCC,MAAM,CAACkB,YAAY,CAACf,QAAQ,CAAC;EAC/B;EAEAgB,cAAcA,CAAChB,QAAQ,EAAE;IACvBd,CAAC,CAAC+B,IAAI,CAAC,IAAI,CAACvB,SAAS,EAAEG,MAAM,IAAI;MAC/BA,MAAM,CAACkB,YAAY,CAACf,QAAQ,CAAC;IAC/B,CAAC,CAAC;EACJ;EAEAkB,OAAOA,CAACC,QAAQ,EAAE;IAChBjC,CAAC,CAAC+B,IAAI,CAAC,IAAI,CAACvB,SAAS,EAAE,CAACG,MAAM,EAAED,IAAI,KAAK;MACvCC,MAAM,CAACqB,OAAO,CAACE,IAAI,IAAI;QACrBD,QAAQ,CAACvB,IAAI,EAAEwB,IAAI,CAAC;MACtB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;;EAEA;EACAC,QAAQA,CAACC,UAAU,EAAE;IACnB,OAAO,IAAI,CAACC,UAAU,CAACnC,QAAQ,CAACa,QAAQ,CAACqB,UAAU,CAAC,CAAC;EACvD;EAEAC,UAAUA,CAACd,OAAO,EAAE;IAClB,OAAOvB,CAAC,CAACsC,GAAG,CAAC,IAAI,CAAC9B,SAAS,EAAE,CAACG,MAAM,EAAED,IAAI,KAAKC,MAAM,CAAC4B,UAAU,CAAChB,OAAO,CAAC,IAAIb,IAAI,CAAC,CAAC8B,MAAM,CACvFC,OACF,CAAC;EACH;EAEAC,QAAQA,CAAC/B,MAAM,EAAEuB,IAAI,EAAE;IACrBA,IAAI,CAACS,IAAI,GAAG,KAAK;IACjB,MAAM;MAACnB;IAAS,CAAC,GAAGU,IAAI;IAExB,MAAMU,KAAK,GAAG,IAAIxC,KAAK,CAAC8B,IAAI,CAACb,GAAG,EAAEa,IAAI,CAAChB,GAAG,EAAEgB,IAAI,CAACb,GAAG,EAAEa,IAAI,CAAChB,GAAG,EAAEM,SAAS,CAAC;IAC1E,IAAIqB,CAAC;IACL,IAAIC,CAAC;;IAEL;IACA,SAASC,KAAKA,CAACC,EAAE,EAAEC,IAAI,EAAE;MACvB,MAAMC,CAAC,GAAGvC,MAAM,CAACwC,UAAU,CAAC3B,SAAS,EAAEwB,EAAE,EAAEd,IAAI,CAAChB,GAAG,CAAC;MACpD,IAAI,CAACgC,CAAC,IAAI,CAACA,CAAC,CAACP,IAAI,EAAE;QACjB,OAAO,KAAK;MACd;MACAC,KAAK,CAACK,IAAI,CAAC,GAAGD,EAAE;MAChBE,CAAC,CAACP,IAAI,GAAG,KAAK;MACd,OAAO,IAAI;IACb;IACA,KAAKG,CAAC,GAAGZ,IAAI,CAACb,GAAG,GAAG,CAAC,EAAE0B,KAAK,CAACD,CAAC,EAAE,KAAK,CAAC,EAAEA,CAAC,EAAE,CAAC;IAC5C,KAAKA,CAAC,GAAGZ,IAAI,CAACb,GAAG,GAAG,CAAC,EAAE0B,KAAK,CAACD,CAAC,EAAE,QAAQ,CAAC,EAAEA,CAAC,EAAE,CAAC;;IAE/C;IACA,SAASM,KAAKA,CAACC,EAAE,EAAEJ,IAAI,EAAE;MACvB,MAAMK,KAAK,GAAG,EAAE;MAChB,KAAKR,CAAC,GAAGF,KAAK,CAAC3B,GAAG,EAAE6B,CAAC,IAAIF,KAAK,CAACtB,MAAM,EAAEwB,CAAC,EAAE,EAAE;QAC1C,MAAMI,CAAC,GAAGvC,MAAM,CAACwC,UAAU,CAAC3B,SAAS,EAAEsB,CAAC,EAAEO,EAAE,CAAC;QAC7C,IAAIH,CAAC,IAAIA,CAAC,CAACP,IAAI,EAAE;UACfW,KAAK,CAACC,IAAI,CAACL,CAAC,CAAC;QACf,CAAC,MAAM;UACL,OAAO,KAAK;QACd;MACF;MACAN,KAAK,CAACK,IAAI,CAAC,GAAGI,EAAE;MAChB,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;QACrCF,KAAK,CAACE,CAAC,CAAC,CAACb,IAAI,GAAG,KAAK;MACvB;MACA,OAAO,IAAI;IACb;IACA,KAAKE,CAAC,GAAGX,IAAI,CAAChB,GAAG,GAAG,CAAC,EAAEkC,KAAK,CAACP,CAAC,EAAE,MAAM,CAAC,EAAEA,CAAC,EAAE,CAAC;IAC7C,KAAKA,CAAC,GAAGX,IAAI,CAAChB,GAAG,GAAG,CAAC,EAAEkC,KAAK,CAACP,CAAC,EAAE,OAAO,CAAC,EAAEA,CAAC,EAAE,CAAC;IAE9C,OAAOD,KAAK;EACd;EAEAc,SAASA,CAAChD,IAAI,EAAEC,MAAM,EAAE;IACtBA,MAAM,GAAGA,MAAM,IAAI,IAAI,CAACH,SAAS,CAACE,IAAI,CAAC;IAEvC,IAAI,CAACC,MAAM,EAAE;MACX,OAAO;QAACD,IAAI;QAAEiD,MAAM,EAAE;MAAE,CAAC;IAC3B;;IAEA;IACAhD,MAAM,CAACqB,OAAO,CAACE,IAAI,IAAI;MACrBA,IAAI,CAACS,IAAI,GAAG,IAAI;IAClB,CAAC,CAAC;IACF,MAAMgB,MAAM,GAAGhD,MAAM,CAClB2B,GAAG,CAACJ,IAAI,IAAIA,IAAI,CAACS,IAAI,IAAI,IAAI,CAACD,QAAQ,CAAC/B,MAAM,EAAEuB,IAAI,CAAC,CAAC,CACrDM,MAAM,CAACC,OAAO,CAAC,CACfH,GAAG,CAACM,KAAK,IAAIA,KAAK,CAACgB,WAAW,CAAC;IAElC,OAAO;MACLlD,IAAI;MACJiD;IACF,CAAC;EACH;EAEAE,eAAeA,CAAClD,MAAM,EAAEa,SAAS,EAAE;IACjC;IACA;IACAb,MAAM,CAACmD,cAAc,CAACtC,SAAS,EAAE,CAACU,IAAI,EAAEb,GAAG,EAAEH,GAAG,KAAK;MACnD,IAAIgB,IAAI,EAAE;QACR,IAAIA,IAAI,CAACb,GAAG,KAAKA,GAAG,IAAIa,IAAI,CAAChB,GAAG,KAAKA,GAAG,EAAE;UACxCgB,IAAI,CAACb,GAAG,GAAGA,GAAG;UACda,IAAI,CAAChB,GAAG,GAAGA,GAAG;UACdgB,IAAI,CAACX,OAAO,GAAGrB,QAAQ,CAACuB,GAAG,CAACP,GAAG,CAAC,GAAGG,GAAG;QACxC;MACF;IACF,CAAC,CAAC;EACJ;EAEA0C,UAAUA,CAACvC,SAAS,EAAEwC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAE;IACjDlE,CAAC,CAAC+B,IAAI,CAAC,IAAI,CAACvB,SAAS,EAAEG,MAAM,IAAI;MAC/BA,MAAM,CAACoD,UAAU,CAACvC,SAAS,EAAEwC,KAAK,EAAEC,SAAS,EAAEC,SAAS,CAAC;MACzD,IAAI,CAACL,eAAe,CAAClD,MAAM,EAAEa,SAAS,CAAC;IACzC,CAAC,CAAC;EACJ;EAEA2C,aAAaA,CAAC3C,SAAS,EAAEwC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAE;IACpDlE,CAAC,CAAC+B,IAAI,CAAC,IAAI,CAACvB,SAAS,EAAEG,MAAM,IAAI;MAC/BA,MAAM,CAACwD,aAAa,CAAC3C,SAAS,EAAEwC,KAAK,EAAEC,SAAS,EAAEC,SAAS,CAAC;MAC5D,IAAI,CAACL,eAAe,CAAClD,MAAM,EAAEa,SAAS,CAAC;IACzC,CAAC,CAAC;EACJ;EAEA,IAAI4C,KAAKA,CAAA,EAAG;IACV;IACA,OAAOpE,CAAC,CAACsC,GAAG,CAAC,IAAI,CAAC9B,SAAS,EAAE,CAACG,MAAM,EAAED,IAAI,KAAK,IAAI,CAACgD,SAAS,CAAChD,IAAI,EAAEC,MAAM,CAAC,CAAC,CAAC6B,MAAM,CACjF6B,WAAW,IAAIA,WAAW,CAACV,MAAM,CAACF,MACpC,CAAC;EACH;EAEA,IAAIW,KAAKA,CAACE,KAAK,EAAE;IACf;IACA,MAAM9D,SAAS,GAAI,IAAI,CAACA,SAAS,GAAG,CAAC,CAAE;IACvC8D,KAAK,CAACtC,OAAO,CAACqC,WAAW,IAAI;MAC3B,MAAM1D,MAAM,GAAIH,SAAS,CAAC6D,WAAW,CAAC3D,IAAI,CAAC,GAAG,IAAIP,UAAU,CAAC,CAAE;MAC/DkE,WAAW,CAACV,MAAM,CAAC3B,OAAO,CAACuC,QAAQ,IAAI;QACrC,IAAIlE,WAAW,CAACmE,IAAI,CAACD,QAAQ,CAACE,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE;UACrD/D,MAAM,CAACgE,OAAO,CAACJ,QAAQ,CAAC;QAC1B;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;AACF;AAEAK,MAAM,CAACC,OAAO,GAAGvE,YAAY"}