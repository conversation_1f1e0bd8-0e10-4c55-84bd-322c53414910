{"version": 3, "file": "typed-stack.js", "names": ["TypedStack", "constructor", "type", "_type", "_stack", "size", "length", "pop", "tos", "push", "instance", "Error", "module", "exports"], "sources": ["../../../lib/utils/typed-stack.js"], "sourcesContent": ["class TypedStack {\n  constructor(type) {\n    this._type = type;\n    this._stack = [];\n  }\n\n  get size() {\n    return this._stack.length;\n  }\n\n  pop() {\n    const tos = this._stack.pop();\n    return tos || new this._type();\n  }\n\n  push(instance) {\n    if (!(instance instanceof this._type)) {\n      throw new Error('Invalid type pushed to TypedStack');\n    }\n    this._stack.push(instance);\n  }\n}\n\nmodule.exports = TypedStack;\n"], "mappings": ";;AAAA,MAAMA,UAAU,CAAC;EACfC,WAAWA,CAACC,IAAI,EAAE;IAChB,IAAI,CAACC,KAAK,GAAGD,IAAI;IACjB,IAAI,CAACE,MAAM,GAAG,EAAE;EAClB;EAEA,IAAIC,IAAIA,CAAA,EAAG;IACT,OAAO,IAAI,CAACD,MAAM,CAACE,MAAM;EAC3B;EAEAC,GAAGA,CAAA,EAAG;IACJ,MAAMC,GAAG,GAAG,IAAI,CAACJ,MAAM,CAACG,GAAG,CAAC,CAAC;IAC7B,OAAOC,GAAG,IAAI,IAAI,IAAI,CAACL,KAAK,CAAC,CAAC;EAChC;EAEAM,IAAIA,CAACC,QAAQ,EAAE;IACb,IAAI,EAAEA,QAAQ,YAAY,IAAI,CAACP,KAAK,CAAC,EAAE;MACrC,MAAM,IAAIQ,KAAK,CAAC,mCAAmC,CAAC;IACtD;IACA,IAAI,CAACP,MAAM,CAACK,IAAI,CAACC,QAAQ,CAAC;EAC5B;AACF;AAEAE,MAAM,CAACC,OAAO,GAAGb,UAAU"}