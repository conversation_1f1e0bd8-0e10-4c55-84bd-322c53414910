/* 用户管理页面样式优化 */

/* 表格单元格样式 */
#usersList td {
  padding: 12px 8px;
  vertical-align: middle;
}

#usersList th {
  padding: 12px 8px;
  background-color: #f3f4f6;
  vertical-align: middle;
  font-weight: 600;
}

/* 设置表格为固定布局，控制列宽 */
#manageUsersSection .min-w-full {
  table-layout: fixed;
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

/* 设置各列的宽度 - 优化列宽分配，所有列居中对齐 */
#manageUsersSection th:nth-child(1),
#manageUsersSection td:nth-child(1) {
  width: 18%;
  text-align: center;
}

#manageUsersSection th:nth-child(2),
#manageUsersSection td:nth-child(2) {
  width: 15%;
  text-align: center;
}

#manageUsersSection th:nth-child(3),
#manageUsersSection td:nth-child(3) {
  width: 12%;
  text-align: center;
}

#manageUsersSection th:nth-child(4),
#manageUsersSection td:nth-child(4) {
  width: 15%;
  text-align: center;
}

#manageUsersSection th:nth-child(5),
#manageUsersSection td:nth-child(5) {
  width: 20%;
  text-align: center;
}

#manageUsersSection th:nth-child(6),
#manageUsersSection td:nth-child(6) {
  width: 20%;
  text-align: center;
}

/* 美化表格行 */
#usersList tr:hover {
  background-color: #f9fafb;
}

/* 表格边框样式 */
#manageUsersSection table {
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  overflow: hidden;
}

#manageUsersSection th {
  border-bottom: 2px solid #e5e7eb;
}

#manageUsersSection td {
  border-bottom: 1px solid #e5e7eb;
}

/* 确保表格内容垂直居中 */
#manageUsersSection td img {
  vertical-align: middle;
  display: inline-block;
}

/* 优化表格内容对齐 */
#manageUsersSection td {
  word-wrap: break-word;
  word-break: break-all;
}



/* 优化电子签名图片显示 */
#manageUsersSection td:nth-child(5) img {
  max-height: 40px;
  max-width: 80px;
  object-fit: contain;
}

/* 优化操作按钮间距 */
#manageUsersSection td:nth-child(6) .edit-user-btn {
  margin-right: 8px;
}

/* 优化表格整体布局 */
#manageUsersSection .overflow-x-auto {
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

/* 恢复默认字体样式 */
#manageUsersSection th {
  font-weight: 600;
  color: #374151;
}

#manageUsersSection td {
  color: #1f2937;
}

/* 表格响应式处理 */
@media (max-width: 768px) {
  #manageUsersSection .min-w-full {
    width: 100%;
    table-layout: auto;
  }

  #manageUsersSection th,
  #manageUsersSection td {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  #manageUsersSection th:nth-child(1),
  #manageUsersSection td:nth-child(1) {
    min-width: 100px;
  }

  #manageUsersSection th:nth-child(2),
  #manageUsersSection td:nth-child(2) {
    min-width: 80px;
  }

  #manageUsersSection th:nth-child(3),
  #manageUsersSection td:nth-child(3) {
    min-width: 70px;
  }

  #manageUsersSection th:nth-child(4),
  #manageUsersSection td:nth-child(4) {
    min-width: 80px;
  }

  #manageUsersSection th:nth-child(5),
  #manageUsersSection td:nth-child(5) {
    min-width: 100px;
  }

  #manageUsersSection th:nth-child(6),
  #manageUsersSection td:nth-child(6) {
    min-width: 80px;
  }
}

/* 编辑按钮样式 */
.edit-user-btn {
  background-color: #2563eb;
  color: white;
  padding: 0.375rem 0.75rem;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.edit-user-btn:hover {
  background-color: #1d4ed8;
}

/* 删除按钮样式 */
.delete-user-btn {
  background-color: #dc2626;
  color: white;
  width: 2rem;
  height: 2rem;
  border-radius: 9999px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.25rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.delete-user-btn:hover {
  background-color: #b91c1c;
}

/* 加载指示器样式 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-container {
  background-color: white;
  padding: 1rem;
  border-radius: 0.375rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  text-align: center;
}

/* 加载动画 */
.spinner {
  width: 40px;
  height: 40px;
  margin: 0 auto;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-left-color: #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 编辑用户模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
}

.modal-container {
  background-color: white;
  border-radius: 0.5rem;
  padding: 1.5rem;
  width: 100%;
  max-width: 32rem;
  max-height: 90vh;
  overflow-y: auto;
}

/* 详情模态框优化样式 */
.detail-modal-optimized {
  max-height: 95vh !important;
  display: flex;
  flex-direction: column;
}

.detail-modal-content {
  flex: 1;
  overflow-y: auto;
  min-height: 0; /* 重要：允许flex子元素收缩 */
}

.detail-modal-actions {
  flex-shrink: 0;
  max-height: 40vh; /* 限制审批操作区域最大高度 */
  overflow-y: auto;
}

/* 经理选择区域优化 */
.managers-selection-optimized {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  padding: 0.5rem;
}

.managers-list-compact {
  max-height: 120px !important;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.modal-title {
  font-size: 1.25rem;
  font-weight: bold;
}

.modal-close {
  color: #6b7280;
  cursor: pointer;
}

.modal-close:hover {
  color: #374151;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  margin-bottom: 1rem;
}

.form-field {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  color: #374151;
  margin-bottom: 0.5rem;
}

.form-input,
.form-select {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
}

.form-input:focus,
.form-select:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
  border-color: #3b82f6;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  margin-top: 1.5rem;
}

.btn-cancel {
  padding: 0.5rem 1rem;
  background-color: #d1d5db;
  color: #1f2937;
  border-radius: 0.375rem;
  cursor: pointer;
}

.btn-cancel:hover {
  background-color: #9ca3af;
}

.btn-save {
  padding: 0.5rem 1rem;
  background-color: #2563eb;
  color: white;
  border-radius: 0.375rem;
  cursor: pointer;
}

.btn-save:hover {
  background-color: #1d4ed8;
}

/* 签名预览样式 */
.signature-preview {
  margin-top: 0.5rem;
}

.signature-image {
  max-height: 4rem;
  /* 删除边框样式，避免电子签名显示黑框 */
}
