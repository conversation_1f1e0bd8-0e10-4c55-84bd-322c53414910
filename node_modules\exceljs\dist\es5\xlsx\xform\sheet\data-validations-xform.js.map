{"version": 3, "file": "data-validations-xform.js", "names": ["_", "require", "utils", "co<PERSON><PERSON><PERSON>", "BaseXform", "Range", "assign", "definedName", "attributes", "name", "defaultValue", "value", "undefined", "assignBool", "parseBoolean", "optimiseDataValidations", "model", "dvList", "map", "dataValidation", "address", "marked", "sort", "a", "b", "strcmp", "dvMap", "keyBy", "matchCol", "addr", "height", "col", "i", "otherAddress", "encodeAddress", "row", "isEqual", "dv", "decodeEx", "dimensions", "sqref", "width", "j", "bottom", "right", "filter", "Boolean", "DataValidationsXform", "tag", "render", "xmlStream", "optimizedModel", "length", "openNode", "count", "for<PERSON>ach", "type", "addAttribute", "operator", "allowBlank", "showInputMessage", "promptTitle", "prompt", "showErrorMessage", "errorStyle", "errorTitle", "error", "formulae", "formula", "index", "writeText", "dateToExcel", "Date", "closeNode", "parseOpen", "node", "_address", "_dataValidation", "_formula", "parseText", "text", "push", "parseClose", "list", "split", "includes", "range", "for<PERSON>ach<PERSON><PERSON><PERSON>", "join", "parseInt", "parseFloat", "excelToDate", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/sheet/data-validations-xform.js"], "sourcesContent": ["const _ = require('../../../utils/under-dash');\nconst utils = require('../../../utils/utils');\nconst colCache = require('../../../utils/col-cache');\nconst BaseXform = require('../base-xform');\nconst Range = require('../../../doc/range');\n\nfunction assign(definedName, attributes, name, defaultValue) {\n  const value = attributes[name];\n  if (value !== undefined) {\n    definedName[name] = value;\n  } else if (defaultValue !== undefined) {\n    definedName[name] = defaultValue;\n  }\n}\n\nfunction assignBool(definedName, attributes, name, defaultValue) {\n  const value = attributes[name];\n  if (value !== undefined) {\n    definedName[name] = utils.parseBoolean(value);\n  } else if (defaultValue !== undefined) {\n    definedName[name] = defaultValue;\n  }\n}\n\nfunction optimiseDataValidations(model) {\n  // Squeeze alike data validations together into rectangular ranges\n  // to reduce file size and speed up Excel load time\n  const dvList = _.map(model, (dataValidation, address) => ({\n    address,\n    dataValidation,\n    marked: false,\n  })).sort((a, b) => _.strcmp(a.address, b.address));\n  const dvMap = _.keyBy(dvList, 'address');\n  const matchCol = (addr, height, col) => {\n    for (let i = 0; i < height; i++) {\n      const otherAddress = colCache.encodeAddress(addr.row + i, col);\n      if (!model[otherAddress] || !_.isEqual(model[addr.address], model[otherAddress])) {\n        return false;\n      }\n    }\n    return true;\n  };\n  return dvList\n    .map(dv => {\n      if (!dv.marked) {\n        const addr = colCache.decodeEx(dv.address);\n        if (addr.dimensions) {\n          dvMap[addr.dimensions].marked = true;\n          return {\n            ...dv.dataValidation,\n            sqref: dv.address,\n          };\n        }\n\n        // iterate downwards - finding matching cells\n        let height = 1;\n        let otherAddress = colCache.encodeAddress(addr.row + height, addr.col);\n        while (model[otherAddress] && _.isEqual(dv.dataValidation, model[otherAddress])) {\n          height++;\n          otherAddress = colCache.encodeAddress(addr.row + height, addr.col);\n        }\n\n        // iterate rightwards...\n\n        let width = 1;\n        while (matchCol(addr, height, addr.col + width)) {\n          width++;\n        }\n\n        // mark all included addresses\n        for (let i = 0; i < height; i++) {\n          for (let j = 0; j < width; j++) {\n            otherAddress = colCache.encodeAddress(addr.row + i, addr.col + j);\n            dvMap[otherAddress].marked = true;\n          }\n        }\n\n        if (height > 1 || width > 1) {\n          const bottom = addr.row + (height - 1);\n          const right = addr.col + (width - 1);\n          return {\n            ...dv.dataValidation,\n            sqref: `${dv.address}:${colCache.encodeAddress(bottom, right)}`,\n          };\n        }\n        return {\n          ...dv.dataValidation,\n          sqref: dv.address,\n        };\n      }\n      return null;\n    })\n    .filter(Boolean);\n}\n\nclass DataValidationsXform extends BaseXform {\n  get tag() {\n    return 'dataValidations';\n  }\n\n  render(xmlStream, model) {\n    const optimizedModel = optimiseDataValidations(model);\n    if (optimizedModel.length) {\n      xmlStream.openNode('dataValidations', {count: optimizedModel.length});\n\n      optimizedModel.forEach(value => {\n        xmlStream.openNode('dataValidation');\n\n        if (value.type !== 'any') {\n          xmlStream.addAttribute('type', value.type);\n\n          if (value.operator && value.type !== 'list' && value.operator !== 'between') {\n            xmlStream.addAttribute('operator', value.operator);\n          }\n          if (value.allowBlank) {\n            xmlStream.addAttribute('allowBlank', '1');\n          }\n        }\n        if (value.showInputMessage) {\n          xmlStream.addAttribute('showInputMessage', '1');\n        }\n        if (value.promptTitle) {\n          xmlStream.addAttribute('promptTitle', value.promptTitle);\n        }\n        if (value.prompt) {\n          xmlStream.addAttribute('prompt', value.prompt);\n        }\n        if (value.showErrorMessage) {\n          xmlStream.addAttribute('showErrorMessage', '1');\n        }\n        if (value.errorStyle) {\n          xmlStream.addAttribute('errorStyle', value.errorStyle);\n        }\n        if (value.errorTitle) {\n          xmlStream.addAttribute('errorTitle', value.errorTitle);\n        }\n        if (value.error) {\n          xmlStream.addAttribute('error', value.error);\n        }\n        xmlStream.addAttribute('sqref', value.sqref);\n        (value.formulae || []).forEach((formula, index) => {\n          xmlStream.openNode(`formula${index + 1}`);\n          if (value.type === 'date') {\n            xmlStream.writeText(utils.dateToExcel(new Date(formula)));\n          } else {\n            xmlStream.writeText(formula);\n          }\n          xmlStream.closeNode();\n        });\n        xmlStream.closeNode();\n      });\n      xmlStream.closeNode();\n    }\n  }\n\n  parseOpen(node) {\n    switch (node.name) {\n      case 'dataValidations':\n        this.model = {};\n        return true;\n\n      case 'dataValidation': {\n        this._address = node.attributes.sqref;\n        const dataValidation = {type: node.attributes.type || 'any', formulae: []};\n\n        if (node.attributes.type) {\n          assignBool(dataValidation, node.attributes, 'allowBlank');\n        }\n        assignBool(dataValidation, node.attributes, 'showInputMessage');\n        assignBool(dataValidation, node.attributes, 'showErrorMessage');\n\n        switch (dataValidation.type) {\n          case 'any':\n          case 'list':\n          case 'custom':\n            break;\n          default:\n            assign(dataValidation, node.attributes, 'operator', 'between');\n            break;\n        }\n        assign(dataValidation, node.attributes, 'promptTitle');\n        assign(dataValidation, node.attributes, 'prompt');\n        assign(dataValidation, node.attributes, 'errorStyle');\n        assign(dataValidation, node.attributes, 'errorTitle');\n        assign(dataValidation, node.attributes, 'error');\n\n        this._dataValidation = dataValidation;\n        return true;\n      }\n\n      case 'formula1':\n      case 'formula2':\n        this._formula = [];\n        return true;\n\n      default:\n        return false;\n    }\n  }\n\n  parseText(text) {\n    if (this._formula) {\n      this._formula.push(text);\n    }\n  }\n\n  parseClose(name) {\n    switch (name) {\n      case 'dataValidations':\n        return false;\n      case 'dataValidation': {\n        if (!this._dataValidation.formulae || !this._dataValidation.formulae.length) {\n          delete this._dataValidation.formulae;\n          delete this._dataValidation.operator;\n        }\n        // The four known cases: 1. E4:L9 N4:U9  2.E4 L9  3. N4:U9  4. E4\n        const list = this._address.split(/\\s+/g) || [];\n        list.forEach(addr => {\n          if (addr.includes(':')) {\n            const range = new Range(addr);\n            range.forEachAddress(address => {\n              this.model[address] = this._dataValidation;\n            });\n          } else {\n            this.model[addr] = this._dataValidation;\n          }\n        });\n        return true;\n      }\n      case 'formula1':\n      case 'formula2': {\n        let formula = this._formula.join('');\n        switch (this._dataValidation.type) {\n          case 'whole':\n          case 'textLength':\n            formula = parseInt(formula, 10);\n            break;\n          case 'decimal':\n            formula = parseFloat(formula);\n            break;\n          case 'date':\n            formula = utils.excelToDate(parseFloat(formula));\n            break;\n          default:\n            break;\n        }\n        this._dataValidation.formulae.push(formula);\n        this._formula = undefined;\n        return true;\n      }\n      default:\n        return true;\n    }\n  }\n}\n\nmodule.exports = DataValidationsXform;\n"], "mappings": ";;AAAA,MAAMA,CAAC,GAAGC,OAAO,CAAC,2BAA2B,CAAC;AAC9C,MAAMC,KAAK,GAAGD,OAAO,CAAC,sBAAsB,CAAC;AAC7C,MAAME,QAAQ,GAAGF,OAAO,CAAC,0BAA0B,CAAC;AACpD,MAAMG,SAAS,GAAGH,OAAO,CAAC,eAAe,CAAC;AAC1C,MAAMI,KAAK,GAAGJ,OAAO,CAAC,oBAAoB,CAAC;AAE3C,SAASK,MAAMA,CAACC,WAAW,EAAEC,UAAU,EAAEC,IAAI,EAAEC,YAAY,EAAE;EAC3D,MAAMC,KAAK,GAAGH,UAAU,CAACC,IAAI,CAAC;EAC9B,IAAIE,KAAK,KAAKC,SAAS,EAAE;IACvBL,WAAW,CAACE,IAAI,CAAC,GAAGE,KAAK;EAC3B,CAAC,MAAM,IAAID,YAAY,KAAKE,SAAS,EAAE;IACrCL,WAAW,CAACE,IAAI,CAAC,GAAGC,YAAY;EAClC;AACF;AAEA,SAASG,UAAUA,CAACN,WAAW,EAAEC,UAAU,EAAEC,IAAI,EAAEC,YAAY,EAAE;EAC/D,MAAMC,KAAK,GAAGH,UAAU,CAACC,IAAI,CAAC;EAC9B,IAAIE,KAAK,KAAKC,SAAS,EAAE;IACvBL,WAAW,CAACE,IAAI,CAAC,GAAGP,KAAK,CAACY,YAAY,CAACH,KAAK,CAAC;EAC/C,CAAC,MAAM,IAAID,YAAY,KAAKE,SAAS,EAAE;IACrCL,WAAW,CAACE,IAAI,CAAC,GAAGC,YAAY;EAClC;AACF;AAEA,SAASK,uBAAuBA,CAACC,KAAK,EAAE;EACtC;EACA;EACA,MAAMC,MAAM,GAAGjB,CAAC,CAACkB,GAAG,CAACF,KAAK,EAAE,CAACG,cAAc,EAAEC,OAAO,MAAM;IACxDA,OAAO;IACPD,cAAc;IACdE,MAAM,EAAE;EACV,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKxB,CAAC,CAACyB,MAAM,CAACF,CAAC,CAACH,OAAO,EAAEI,CAAC,CAACJ,OAAO,CAAC,CAAC;EAClD,MAAMM,KAAK,GAAG1B,CAAC,CAAC2B,KAAK,CAACV,MAAM,EAAE,SAAS,CAAC;EACxC,MAAMW,QAAQ,GAAGA,CAACC,IAAI,EAAEC,MAAM,EAAEC,GAAG,KAAK;IACtC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,EAAEE,CAAC,EAAE,EAAE;MAC/B,MAAMC,YAAY,GAAG9B,QAAQ,CAAC+B,aAAa,CAACL,IAAI,CAACM,GAAG,GAAGH,CAAC,EAAED,GAAG,CAAC;MAC9D,IAAI,CAACf,KAAK,CAACiB,YAAY,CAAC,IAAI,CAACjC,CAAC,CAACoC,OAAO,CAACpB,KAAK,CAACa,IAAI,CAACT,OAAO,CAAC,EAAEJ,KAAK,CAACiB,YAAY,CAAC,CAAC,EAAE;QAChF,OAAO,KAAK;MACd;IACF;IACA,OAAO,IAAI;EACb,CAAC;EACD,OAAOhB,MAAM,CACVC,GAAG,CAACmB,EAAE,IAAI;IACT,IAAI,CAACA,EAAE,CAAChB,MAAM,EAAE;MACd,MAAMQ,IAAI,GAAG1B,QAAQ,CAACmC,QAAQ,CAACD,EAAE,CAACjB,OAAO,CAAC;MAC1C,IAAIS,IAAI,CAACU,UAAU,EAAE;QACnBb,KAAK,CAACG,IAAI,CAACU,UAAU,CAAC,CAAClB,MAAM,GAAG,IAAI;QACpC,OAAO;UACL,GAAGgB,EAAE,CAAClB,cAAc;UACpBqB,KAAK,EAAEH,EAAE,CAACjB;QACZ,CAAC;MACH;;MAEA;MACA,IAAIU,MAAM,GAAG,CAAC;MACd,IAAIG,YAAY,GAAG9B,QAAQ,CAAC+B,aAAa,CAACL,IAAI,CAACM,GAAG,GAAGL,MAAM,EAAED,IAAI,CAACE,GAAG,CAAC;MACtE,OAAOf,KAAK,CAACiB,YAAY,CAAC,IAAIjC,CAAC,CAACoC,OAAO,CAACC,EAAE,CAAClB,cAAc,EAAEH,KAAK,CAACiB,YAAY,CAAC,CAAC,EAAE;QAC/EH,MAAM,EAAE;QACRG,YAAY,GAAG9B,QAAQ,CAAC+B,aAAa,CAACL,IAAI,CAACM,GAAG,GAAGL,MAAM,EAAED,IAAI,CAACE,GAAG,CAAC;MACpE;;MAEA;;MAEA,IAAIU,KAAK,GAAG,CAAC;MACb,OAAOb,QAAQ,CAACC,IAAI,EAAEC,MAAM,EAAED,IAAI,CAACE,GAAG,GAAGU,KAAK,CAAC,EAAE;QAC/CA,KAAK,EAAE;MACT;;MAEA;MACA,KAAK,IAAIT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,EAAEE,CAAC,EAAE,EAAE;QAC/B,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,EAAEC,CAAC,EAAE,EAAE;UAC9BT,YAAY,GAAG9B,QAAQ,CAAC+B,aAAa,CAACL,IAAI,CAACM,GAAG,GAAGH,CAAC,EAAEH,IAAI,CAACE,GAAG,GAAGW,CAAC,CAAC;UACjEhB,KAAK,CAACO,YAAY,CAAC,CAACZ,MAAM,GAAG,IAAI;QACnC;MACF;MAEA,IAAIS,MAAM,GAAG,CAAC,IAAIW,KAAK,GAAG,CAAC,EAAE;QAC3B,MAAME,MAAM,GAAGd,IAAI,CAACM,GAAG,IAAIL,MAAM,GAAG,CAAC,CAAC;QACtC,MAAMc,KAAK,GAAGf,IAAI,CAACE,GAAG,IAAIU,KAAK,GAAG,CAAC,CAAC;QACpC,OAAO;UACL,GAAGJ,EAAE,CAAClB,cAAc;UACpBqB,KAAK,EAAG,GAAEH,EAAE,CAACjB,OAAQ,IAAGjB,QAAQ,CAAC+B,aAAa,CAACS,MAAM,EAAEC,KAAK,CAAE;QAChE,CAAC;MACH;MACA,OAAO;QACL,GAAGP,EAAE,CAAClB,cAAc;QACpBqB,KAAK,EAAEH,EAAE,CAACjB;MACZ,CAAC;IACH;IACA,OAAO,IAAI;EACb,CAAC,CAAC,CACDyB,MAAM,CAACC,OAAO,CAAC;AACpB;AAEA,MAAMC,oBAAoB,SAAS3C,SAAS,CAAC;EAC3C,IAAI4C,GAAGA,CAAA,EAAG;IACR,OAAO,iBAAiB;EAC1B;EAEAC,MAAMA,CAACC,SAAS,EAAElC,KAAK,EAAE;IACvB,MAAMmC,cAAc,GAAGpC,uBAAuB,CAACC,KAAK,CAAC;IACrD,IAAImC,cAAc,CAACC,MAAM,EAAE;MACzBF,SAAS,CAACG,QAAQ,CAAC,iBAAiB,EAAE;QAACC,KAAK,EAAEH,cAAc,CAACC;MAAM,CAAC,CAAC;MAErED,cAAc,CAACI,OAAO,CAAC5C,KAAK,IAAI;QAC9BuC,SAAS,CAACG,QAAQ,CAAC,gBAAgB,CAAC;QAEpC,IAAI1C,KAAK,CAAC6C,IAAI,KAAK,KAAK,EAAE;UACxBN,SAAS,CAACO,YAAY,CAAC,MAAM,EAAE9C,KAAK,CAAC6C,IAAI,CAAC;UAE1C,IAAI7C,KAAK,CAAC+C,QAAQ,IAAI/C,KAAK,CAAC6C,IAAI,KAAK,MAAM,IAAI7C,KAAK,CAAC+C,QAAQ,KAAK,SAAS,EAAE;YAC3ER,SAAS,CAACO,YAAY,CAAC,UAAU,EAAE9C,KAAK,CAAC+C,QAAQ,CAAC;UACpD;UACA,IAAI/C,KAAK,CAACgD,UAAU,EAAE;YACpBT,SAAS,CAACO,YAAY,CAAC,YAAY,EAAE,GAAG,CAAC;UAC3C;QACF;QACA,IAAI9C,KAAK,CAACiD,gBAAgB,EAAE;UAC1BV,SAAS,CAACO,YAAY,CAAC,kBAAkB,EAAE,GAAG,CAAC;QACjD;QACA,IAAI9C,KAAK,CAACkD,WAAW,EAAE;UACrBX,SAAS,CAACO,YAAY,CAAC,aAAa,EAAE9C,KAAK,CAACkD,WAAW,CAAC;QAC1D;QACA,IAAIlD,KAAK,CAACmD,MAAM,EAAE;UAChBZ,SAAS,CAACO,YAAY,CAAC,QAAQ,EAAE9C,KAAK,CAACmD,MAAM,CAAC;QAChD;QACA,IAAInD,KAAK,CAACoD,gBAAgB,EAAE;UAC1Bb,SAAS,CAACO,YAAY,CAAC,kBAAkB,EAAE,GAAG,CAAC;QACjD;QACA,IAAI9C,KAAK,CAACqD,UAAU,EAAE;UACpBd,SAAS,CAACO,YAAY,CAAC,YAAY,EAAE9C,KAAK,CAACqD,UAAU,CAAC;QACxD;QACA,IAAIrD,KAAK,CAACsD,UAAU,EAAE;UACpBf,SAAS,CAACO,YAAY,CAAC,YAAY,EAAE9C,KAAK,CAACsD,UAAU,CAAC;QACxD;QACA,IAAItD,KAAK,CAACuD,KAAK,EAAE;UACfhB,SAAS,CAACO,YAAY,CAAC,OAAO,EAAE9C,KAAK,CAACuD,KAAK,CAAC;QAC9C;QACAhB,SAAS,CAACO,YAAY,CAAC,OAAO,EAAE9C,KAAK,CAAC6B,KAAK,CAAC;QAC5C,CAAC7B,KAAK,CAACwD,QAAQ,IAAI,EAAE,EAAEZ,OAAO,CAAC,CAACa,OAAO,EAAEC,KAAK,KAAK;UACjDnB,SAAS,CAACG,QAAQ,CAAE,UAASgB,KAAK,GAAG,CAAE,EAAC,CAAC;UACzC,IAAI1D,KAAK,CAAC6C,IAAI,KAAK,MAAM,EAAE;YACzBN,SAAS,CAACoB,SAAS,CAACpE,KAAK,CAACqE,WAAW,CAAC,IAAIC,IAAI,CAACJ,OAAO,CAAC,CAAC,CAAC;UAC3D,CAAC,MAAM;YACLlB,SAAS,CAACoB,SAAS,CAACF,OAAO,CAAC;UAC9B;UACAlB,SAAS,CAACuB,SAAS,CAAC,CAAC;QACvB,CAAC,CAAC;QACFvB,SAAS,CAACuB,SAAS,CAAC,CAAC;MACvB,CAAC,CAAC;MACFvB,SAAS,CAACuB,SAAS,CAAC,CAAC;IACvB;EACF;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,QAAQA,IAAI,CAAClE,IAAI;MACf,KAAK,iBAAiB;QACpB,IAAI,CAACO,KAAK,GAAG,CAAC,CAAC;QACf,OAAO,IAAI;MAEb,KAAK,gBAAgB;QAAE;UACrB,IAAI,CAAC4D,QAAQ,GAAGD,IAAI,CAACnE,UAAU,CAACgC,KAAK;UACrC,MAAMrB,cAAc,GAAG;YAACqC,IAAI,EAAEmB,IAAI,CAACnE,UAAU,CAACgD,IAAI,IAAI,KAAK;YAAEW,QAAQ,EAAE;UAAE,CAAC;UAE1E,IAAIQ,IAAI,CAACnE,UAAU,CAACgD,IAAI,EAAE;YACxB3C,UAAU,CAACM,cAAc,EAAEwD,IAAI,CAACnE,UAAU,EAAE,YAAY,CAAC;UAC3D;UACAK,UAAU,CAACM,cAAc,EAAEwD,IAAI,CAACnE,UAAU,EAAE,kBAAkB,CAAC;UAC/DK,UAAU,CAACM,cAAc,EAAEwD,IAAI,CAACnE,UAAU,EAAE,kBAAkB,CAAC;UAE/D,QAAQW,cAAc,CAACqC,IAAI;YACzB,KAAK,KAAK;YACV,KAAK,MAAM;YACX,KAAK,QAAQ;cACX;YACF;cACElD,MAAM,CAACa,cAAc,EAAEwD,IAAI,CAACnE,UAAU,EAAE,UAAU,EAAE,SAAS,CAAC;cAC9D;UACJ;UACAF,MAAM,CAACa,cAAc,EAAEwD,IAAI,CAACnE,UAAU,EAAE,aAAa,CAAC;UACtDF,MAAM,CAACa,cAAc,EAAEwD,IAAI,CAACnE,UAAU,EAAE,QAAQ,CAAC;UACjDF,MAAM,CAACa,cAAc,EAAEwD,IAAI,CAACnE,UAAU,EAAE,YAAY,CAAC;UACrDF,MAAM,CAACa,cAAc,EAAEwD,IAAI,CAACnE,UAAU,EAAE,YAAY,CAAC;UACrDF,MAAM,CAACa,cAAc,EAAEwD,IAAI,CAACnE,UAAU,EAAE,OAAO,CAAC;UAEhD,IAAI,CAACqE,eAAe,GAAG1D,cAAc;UACrC,OAAO,IAAI;QACb;MAEA,KAAK,UAAU;MACf,KAAK,UAAU;QACb,IAAI,CAAC2D,QAAQ,GAAG,EAAE;QAClB,OAAO,IAAI;MAEb;QACE,OAAO,KAAK;IAChB;EACF;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACF,QAAQ,EAAE;MACjB,IAAI,CAACA,QAAQ,CAACG,IAAI,CAACD,IAAI,CAAC;IAC1B;EACF;EAEAE,UAAUA,CAACzE,IAAI,EAAE;IACf,QAAQA,IAAI;MACV,KAAK,iBAAiB;QACpB,OAAO,KAAK;MACd,KAAK,gBAAgB;QAAE;UACrB,IAAI,CAAC,IAAI,CAACoE,eAAe,CAACV,QAAQ,IAAI,CAAC,IAAI,CAACU,eAAe,CAACV,QAAQ,CAACf,MAAM,EAAE;YAC3E,OAAO,IAAI,CAACyB,eAAe,CAACV,QAAQ;YACpC,OAAO,IAAI,CAACU,eAAe,CAACnB,QAAQ;UACtC;UACA;UACA,MAAMyB,IAAI,GAAG,IAAI,CAACP,QAAQ,CAACQ,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE;UAC9CD,IAAI,CAAC5B,OAAO,CAAC1B,IAAI,IAAI;YACnB,IAAIA,IAAI,CAACwD,QAAQ,CAAC,GAAG,CAAC,EAAE;cACtB,MAAMC,KAAK,GAAG,IAAIjF,KAAK,CAACwB,IAAI,CAAC;cAC7ByD,KAAK,CAACC,cAAc,CAACnE,OAAO,IAAI;gBAC9B,IAAI,CAACJ,KAAK,CAACI,OAAO,CAAC,GAAG,IAAI,CAACyD,eAAe;cAC5C,CAAC,CAAC;YACJ,CAAC,MAAM;cACL,IAAI,CAAC7D,KAAK,CAACa,IAAI,CAAC,GAAG,IAAI,CAACgD,eAAe;YACzC;UACF,CAAC,CAAC;UACF,OAAO,IAAI;QACb;MACA,KAAK,UAAU;MACf,KAAK,UAAU;QAAE;UACf,IAAIT,OAAO,GAAG,IAAI,CAACU,QAAQ,CAACU,IAAI,CAAC,EAAE,CAAC;UACpC,QAAQ,IAAI,CAACX,eAAe,CAACrB,IAAI;YAC/B,KAAK,OAAO;YACZ,KAAK,YAAY;cACfY,OAAO,GAAGqB,QAAQ,CAACrB,OAAO,EAAE,EAAE,CAAC;cAC/B;YACF,KAAK,SAAS;cACZA,OAAO,GAAGsB,UAAU,CAACtB,OAAO,CAAC;cAC7B;YACF,KAAK,MAAM;cACTA,OAAO,GAAGlE,KAAK,CAACyF,WAAW,CAACD,UAAU,CAACtB,OAAO,CAAC,CAAC;cAChD;YACF;cACE;UACJ;UACA,IAAI,CAACS,eAAe,CAACV,QAAQ,CAACc,IAAI,CAACb,OAAO,CAAC;UAC3C,IAAI,CAACU,QAAQ,GAAGlE,SAAS;UACzB,OAAO,IAAI;QACb;MACA;QACE,OAAO,IAAI;IACf;EACF;AACF;AAEAgF,MAAM,CAACC,OAAO,GAAG9C,oBAAoB"}