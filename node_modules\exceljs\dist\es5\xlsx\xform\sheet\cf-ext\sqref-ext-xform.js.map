{"version": 3, "file": "sqref-ext-xform.js", "names": ["BaseXform", "require", "SqrefExtXform", "tag", "render", "xmlStream", "model", "leafNode", "parseOpen", "parseText", "text", "parseClose", "name", "module", "exports"], "sources": ["../../../../../../lib/xlsx/xform/sheet/cf-ext/sqref-ext-xform.js"], "sourcesContent": ["const BaseXform = require('../../base-xform');\n\nclass SqrefExtXform extends BaseXform {\n  get tag() {\n    return 'xm:sqref';\n  }\n\n  render(xmlStream, model) {\n    xmlStream.leafNode(this.tag, null, model);\n  }\n\n  parseOpen() {\n    this.model = '';\n  }\n\n  parseText(text) {\n    this.model += text;\n  }\n\n  parseClose(name) {\n    return name !== this.tag;\n  }\n}\n\nmodule.exports = SqrefExtXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AAE7C,MAAMC,aAAa,SAASF,SAAS,CAAC;EACpC,IAAIG,GAAGA,CAAA,EAAG;IACR,OAAO,UAAU;EACnB;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvBD,SAAS,CAACE,QAAQ,CAAC,IAAI,CAACJ,GAAG,EAAE,IAAI,EAAEG,KAAK,CAAC;EAC3C;EAEAE,SAASA,CAAA,EAAG;IACV,IAAI,CAACF,KAAK,GAAG,EAAE;EACjB;EAEAG,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,CAACJ,KAAK,IAAII,IAAI;EACpB;EAEAC,UAAUA,CAACC,IAAI,EAAE;IACf,OAAOA,IAAI,KAAK,IAAI,CAACT,GAAG;EAC1B;AACF;AAEAU,MAAM,CAACC,OAAO,GAAGZ,aAAa"}