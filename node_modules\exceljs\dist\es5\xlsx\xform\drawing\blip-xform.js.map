{"version": 3, "file": "blip-xform.js", "names": ["BaseXform", "require", "BlipXform", "tag", "render", "xmlStream", "model", "leafNode", "rId", "cstate", "parseOpen", "node", "name", "attributes", "parseText", "parseClose", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/drawing/blip-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\n\nclass BlipXform extends BaseXform {\n  get tag() {\n    return 'a:blip';\n  }\n\n  render(xmlStream, model) {\n    xmlStream.leafNode(this.tag, {\n      'xmlns:r': 'http://schemas.openxmlformats.org/officeDocument/2006/relationships',\n      'r:embed': model.rId,\n      cstate: 'print',\n    });\n    // TODO: handle children (e.g. a:extLst=>a:ext=>a14:useLocalDpi\n  }\n\n  parseOpen(node) {\n    switch (node.name) {\n      case this.tag:\n        this.model = {\n          rId: node.attributes['r:embed'],\n        };\n        return true;\n      default:\n        return true;\n    }\n  }\n\n  parseText() {}\n\n  parseClose(name) {\n    switch (name) {\n      case this.tag:\n        return false;\n      default:\n        // unprocessed internal nodes\n        return true;\n    }\n  }\n}\n\nmodule.exports = BlipXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAMC,SAAS,SAASF,SAAS,CAAC;EAChC,IAAIG,GAAGA,CAAA,EAAG;IACR,OAAO,QAAQ;EACjB;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvBD,SAAS,CAACE,QAAQ,CAAC,IAAI,CAACJ,GAAG,EAAE;MAC3B,SAAS,EAAE,qEAAqE;MAChF,SAAS,EAAEG,KAAK,CAACE,GAAG;MACpBC,MAAM,EAAE;IACV,CAAC,CAAC;IACF;EACF;;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,QAAQA,IAAI,CAACC,IAAI;MACf,KAAK,IAAI,CAACT,GAAG;QACX,IAAI,CAACG,KAAK,GAAG;UACXE,GAAG,EAAEG,IAAI,CAACE,UAAU,CAAC,SAAS;QAChC,CAAC;QACD,OAAO,IAAI;MACb;QACE,OAAO,IAAI;IACf;EACF;EAEAC,SAASA,CAAA,EAAG,CAAC;EAEbC,UAAUA,CAACH,IAAI,EAAE;IACf,QAAQA,IAAI;MACV,KAAK,IAAI,CAACT,GAAG;QACX,OAAO,KAAK;MACd;QACE;QACA,OAAO,IAAI;IACf;EACF;AACF;AAEAa,MAAM,CAACC,OAAO,GAAGf,SAAS"}