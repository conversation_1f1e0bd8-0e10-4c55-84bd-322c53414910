{"version": 3, "file": "ext-xform.js", "names": ["BaseXform", "require", "EMU_PER_PIXEL_AT_96_DPI", "ExtXform", "constructor", "options", "tag", "map", "render", "xmlStream", "model", "openNode", "width", "Math", "floor", "height", "addAttribute", "closeNode", "parseOpen", "node", "name", "parseInt", "attributes", "cx", "cy", "parseText", "parseClose", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/drawing/ext-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\n\n/** https://en.wikipedia.org/wiki/Office_Open_XML_file_formats#DrawingML */\nconst EMU_PER_PIXEL_AT_96_DPI = 9525;\n\nclass ExtXform extends BaseXform {\n  constructor(options) {\n    super();\n\n    this.tag = options.tag;\n    this.map = {};\n  }\n\n  render(xmlStream, model) {\n    xmlStream.openNode(this.tag);\n\n    const width = Math.floor(model.width * EMU_PER_PIXEL_AT_96_DPI);\n    const height = Math.floor(model.height * EMU_PER_PIXEL_AT_96_DPI);\n\n    xmlStream.addAttribute('cx', width);\n    xmlStream.addAttribute('cy', height);\n\n    xmlStream.closeNode();\n  }\n\n  parseOpen(node) {\n    if (node.name === this.tag) {\n      this.model = {\n        width: parseInt(node.attributes.cx || '0', 10) / EMU_PER_PIXEL_AT_96_DPI,\n        height: parseInt(node.attributes.cy || '0', 10) / EMU_PER_PIXEL_AT_96_DPI,\n      };\n      return true;\n    }\n    return false;\n  }\n\n  parseText(/* text */) {}\n\n  parseClose(/* name */) {\n    return false;\n  }\n}\n\nmodule.exports = ExtXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;;AAE1C;AACA,MAAMC,uBAAuB,GAAG,IAAI;AAEpC,MAAMC,QAAQ,SAASH,SAAS,CAAC;EAC/BI,WAAWA,CAACC,OAAO,EAAE;IACnB,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,GAAG,GAAGD,OAAO,CAACC,GAAG;IACtB,IAAI,CAACC,GAAG,GAAG,CAAC,CAAC;EACf;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvBD,SAAS,CAACE,QAAQ,CAAC,IAAI,CAACL,GAAG,CAAC;IAE5B,MAAMM,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACJ,KAAK,CAACE,KAAK,GAAGV,uBAAuB,CAAC;IAC/D,MAAMa,MAAM,GAAGF,IAAI,CAACC,KAAK,CAACJ,KAAK,CAACK,MAAM,GAAGb,uBAAuB,CAAC;IAEjEO,SAAS,CAACO,YAAY,CAAC,IAAI,EAAEJ,KAAK,CAAC;IACnCH,SAAS,CAACO,YAAY,CAAC,IAAI,EAAED,MAAM,CAAC;IAEpCN,SAAS,CAACQ,SAAS,CAAC,CAAC;EACvB;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,IAAIA,IAAI,CAACC,IAAI,KAAK,IAAI,CAACd,GAAG,EAAE;MAC1B,IAAI,CAACI,KAAK,GAAG;QACXE,KAAK,EAAES,QAAQ,CAACF,IAAI,CAACG,UAAU,CAACC,EAAE,IAAI,GAAG,EAAE,EAAE,CAAC,GAAGrB,uBAAuB;QACxEa,MAAM,EAAEM,QAAQ,CAACF,IAAI,CAACG,UAAU,CAACE,EAAE,IAAI,GAAG,EAAE,EAAE,CAAC,GAAGtB;MACpD,CAAC;MACD,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAEAuB,SAASA,CAAA,CAAC,YAAY,CAAC;EAEvBC,UAAUA,CAAA,CAAC;EAAA,EAAY;IACrB,OAAO,KAAK;EACd;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGzB,QAAQ"}