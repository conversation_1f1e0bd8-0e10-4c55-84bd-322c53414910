{"version": 3, "file": "date-xform.js", "names": ["BaseXform", "require", "DateXform", "constructor", "options", "tag", "attr", "attrs", "_format", "format", "dt", "Number", "isNaN", "getTime", "toISOString", "e", "_parse", "parse", "str", "Date", "render", "xmlStream", "model", "openNode", "addAttributes", "addAttribute", "writeText", "closeNode", "parseOpen", "node", "name", "attributes", "text", "parseText", "push", "parseClose", "join", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/simple/date-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\n\nclass DateXform extends BaseXform {\n  constructor(options) {\n    super();\n\n    this.tag = options.tag;\n    this.attr = options.attr;\n    this.attrs = options.attrs;\n    this._format =\n      options.format ||\n      function(dt) {\n        try {\n          if (Number.isNaN(dt.getTime())) return '';\n          return dt.toISOString();\n        } catch (e) {\n          return '';\n        }\n      };\n    this._parse =\n      options.parse ||\n      function(str) {\n        return new Date(str);\n      };\n  }\n\n  render(xmlStream, model) {\n    if (model) {\n      xmlStream.openNode(this.tag);\n      if (this.attrs) {\n        xmlStream.addAttributes(this.attrs);\n      }\n      if (this.attr) {\n        xmlStream.addAttribute(this.attr, this._format(model));\n      } else {\n        xmlStream.writeText(this._format(model));\n      }\n      xmlStream.closeNode();\n    }\n  }\n\n  parseOpen(node) {\n    if (node.name === this.tag) {\n      if (this.attr) {\n        this.model = this._parse(node.attributes[this.attr]);\n      } else {\n        this.text = [];\n      }\n    }\n  }\n\n  parseText(text) {\n    if (!this.attr) {\n      this.text.push(text);\n    }\n  }\n\n  parseClose() {\n    if (!this.attr) {\n      this.model = this._parse(this.text.join(''));\n    }\n    return false;\n  }\n}\n\nmodule.exports = DateXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAMC,SAAS,SAASF,SAAS,CAAC;EAChCG,WAAWA,CAACC,OAAO,EAAE;IACnB,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,GAAG,GAAGD,OAAO,CAACC,GAAG;IACtB,IAAI,CAACC,IAAI,GAAGF,OAAO,CAACE,IAAI;IACxB,IAAI,CAACC,KAAK,GAAGH,OAAO,CAACG,KAAK;IAC1B,IAAI,CAACC,OAAO,GACVJ,OAAO,CAACK,MAAM,IACd,UAASC,EAAE,EAAE;MACX,IAAI;QACF,IAAIC,MAAM,CAACC,KAAK,CAACF,EAAE,CAACG,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE;QACzC,OAAOH,EAAE,CAACI,WAAW,CAAC,CAAC;MACzB,CAAC,CAAC,OAAOC,CAAC,EAAE;QACV,OAAO,EAAE;MACX;IACF,CAAC;IACH,IAAI,CAACC,MAAM,GACTZ,OAAO,CAACa,KAAK,IACb,UAASC,GAAG,EAAE;MACZ,OAAO,IAAIC,IAAI,CAACD,GAAG,CAAC;IACtB,CAAC;EACL;EAEAE,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvB,IAAIA,KAAK,EAAE;MACTD,SAAS,CAACE,QAAQ,CAAC,IAAI,CAAClB,GAAG,CAAC;MAC5B,IAAI,IAAI,CAACE,KAAK,EAAE;QACdc,SAAS,CAACG,aAAa,CAAC,IAAI,CAACjB,KAAK,CAAC;MACrC;MACA,IAAI,IAAI,CAACD,IAAI,EAAE;QACbe,SAAS,CAACI,YAAY,CAAC,IAAI,CAACnB,IAAI,EAAE,IAAI,CAACE,OAAO,CAACc,KAAK,CAAC,CAAC;MACxD,CAAC,MAAM;QACLD,SAAS,CAACK,SAAS,CAAC,IAAI,CAAClB,OAAO,CAACc,KAAK,CAAC,CAAC;MAC1C;MACAD,SAAS,CAACM,SAAS,CAAC,CAAC;IACvB;EACF;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,IAAIA,IAAI,CAACC,IAAI,KAAK,IAAI,CAACzB,GAAG,EAAE;MAC1B,IAAI,IAAI,CAACC,IAAI,EAAE;QACb,IAAI,CAACgB,KAAK,GAAG,IAAI,CAACN,MAAM,CAACa,IAAI,CAACE,UAAU,CAAC,IAAI,CAACzB,IAAI,CAAC,CAAC;MACtD,CAAC,MAAM;QACL,IAAI,CAAC0B,IAAI,GAAG,EAAE;MAChB;IACF;EACF;EAEAC,SAASA,CAACD,IAAI,EAAE;IACd,IAAI,CAAC,IAAI,CAAC1B,IAAI,EAAE;MACd,IAAI,CAAC0B,IAAI,CAACE,IAAI,CAACF,IAAI,CAAC;IACtB;EACF;EAEAG,UAAUA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAAC7B,IAAI,EAAE;MACd,IAAI,CAACgB,KAAK,GAAG,IAAI,CAACN,MAAM,CAAC,IAAI,CAACgB,IAAI,CAACI,IAAI,CAAC,EAAE,CAAC,CAAC;IAC9C;IACA,OAAO,KAAK;EACd;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGpC,SAAS"}