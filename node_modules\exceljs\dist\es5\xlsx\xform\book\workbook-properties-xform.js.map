{"version": 3, "file": "workbook-properties-xform.js", "names": ["BaseXform", "require", "WorksheetPropertiesXform", "render", "xmlStream", "model", "leafNode", "date1904", "undefined", "defaultThemeVersion", "filterPrivacy", "parseOpen", "node", "name", "attributes", "parseText", "parseClose", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/book/workbook-properties-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\n\nclass WorksheetPropertiesXform extends BaseXform {\n  render(xmlStream, model) {\n    xmlStream.leafNode('workbookPr', {\n      date1904: model.date1904 ? 1 : undefined,\n      defaultThemeVersion: 164011,\n      filterPrivacy: 1,\n    });\n  }\n\n  parseOpen(node) {\n    if (node.name === 'workbookPr') {\n      this.model = {\n        date1904: node.attributes.date1904 === '1',\n      };\n      return true;\n    }\n    return false;\n  }\n\n  parseText() {}\n\n  parseClose() {\n    return false;\n  }\n}\n\nmodule.exports = WorksheetPropertiesXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAMC,wBAAwB,SAASF,SAAS,CAAC;EAC/CG,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvBD,SAAS,CAACE,QAAQ,CAAC,YAAY,EAAE;MAC/BC,QAAQ,EAAEF,KAAK,CAACE,QAAQ,GAAG,CAAC,GAAGC,SAAS;MACxCC,mBAAmB,EAAE,MAAM;MAC3BC,aAAa,EAAE;IACjB,CAAC,CAAC;EACJ;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,IAAIA,IAAI,CAACC,IAAI,KAAK,YAAY,EAAE;MAC9B,IAAI,CAACR,KAAK,GAAG;QACXE,QAAQ,EAAEK,IAAI,CAACE,UAAU,CAACP,QAAQ,KAAK;MACzC,CAAC;MACD,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAEAQ,SAASA,CAAA,EAAG,CAAC;EAEbC,UAAUA,CAAA,EAAG;IACX,OAAO,KAAK;EACd;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGhB,wBAAwB"}