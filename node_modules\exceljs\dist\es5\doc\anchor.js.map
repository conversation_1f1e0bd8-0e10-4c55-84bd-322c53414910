{"version": 3, "file": "anchor.js", "names": ["co<PERSON><PERSON><PERSON>", "require", "<PERSON><PERSON>", "constructor", "worksheet", "address", "offset", "arguments", "length", "undefined", "nativeCol", "nativeColOff", "nativeRow", "nativeRowOff", "decoded", "decode<PERSON>ddress", "col", "row", "asInstance", "model", "Math", "min", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v", "floor", "rowHeight", "getColumn", "isCustomWidth", "width", "getRow", "height", "value", "module", "exports"], "sources": ["../../../lib/doc/anchor.js"], "sourcesContent": ["'use strict';\n\nconst colCache = require('../utils/col-cache');\n\nclass Anchor {\n  constructor(worksheet, address, offset = 0) {\n    this.worksheet = worksheet;\n\n    if (!address) {\n      this.nativeCol = 0;\n      this.nativeColOff = 0;\n      this.nativeRow = 0;\n      this.nativeRowOff = 0;\n    } else if (typeof address === 'string') {\n      const decoded = colCache.decodeAddress(address);\n      this.nativeCol = decoded.col + offset;\n      this.nativeColOff = 0;\n      this.nativeRow = decoded.row + offset;\n      this.nativeRowOff = 0;\n    } else if (address.nativeCol !== undefined) {\n      this.nativeCol = address.nativeCol || 0;\n      this.nativeColOff = address.nativeColOff || 0;\n      this.nativeRow = address.nativeRow || 0;\n      this.nativeRowOff = address.nativeRowOff || 0;\n    } else if (address.col !== undefined) {\n      this.col = address.col + offset;\n      this.row = address.row + offset;\n    } else {\n      this.nativeCol = 0;\n      this.nativeColOff = 0;\n      this.nativeRow = 0;\n      this.nativeRowOff = 0;\n    }\n  }\n\n  static asInstance(model) {\n    return model instanceof Anchor || model == null ? model : new Anchor(model);\n  }\n\n  get col() {\n    return this.nativeCol + (Math.min(this.colWidth - 1, this.nativeColOff) / this.colWidth);\n  }\n\n  set col(v) {\n    this.nativeCol = Math.floor(v);\n    this.nativeColOff = Math.floor((v - this.nativeCol) * this.colWidth);\n  }\n\n  get row() {\n    return this.nativeRow + (Math.min(this.rowHeight - 1, this.nativeRowOff) / this.rowHeight);\n  }\n\n  set row(v) {\n    this.nativeRow = Math.floor(v);\n    this.nativeRowOff = Math.floor((v - this.nativeRow) * this.rowHeight);\n  }\n\n  get colWidth() {\n    return this.worksheet &&\n      this.worksheet.getColumn(this.nativeCol + 1) &&\n      this.worksheet.getColumn(this.nativeCol + 1).isCustomWidth\n      ? Math.floor(this.worksheet.getColumn(this.nativeCol + 1).width * 10000)\n      : 640000;\n  }\n\n  get rowHeight() {\n    return this.worksheet &&\n      this.worksheet.getRow(this.nativeRow + 1) &&\n      this.worksheet.getRow(this.nativeRow + 1).height\n      ? Math.floor(this.worksheet.getRow(this.nativeRow + 1).height * 10000)\n      : 180000;\n  }\n\n  get model() {\n    return {\n      nativeCol: this.nativeCol,\n      nativeColOff: this.nativeColOff,\n      nativeRow: this.nativeRow,\n      nativeRowOff: this.nativeRowOff,\n    };\n  }\n\n  set model(value) {\n    this.nativeCol = value.nativeCol;\n    this.nativeColOff = value.nativeColOff;\n    this.nativeRow = value.nativeRow;\n    this.nativeRowOff = value.nativeRowOff;\n  }\n}\n\nmodule.exports = Anchor;\n"], "mappings": "AAAA,YAAY;;AAEZ,MAAMA,QAAQ,GAAGC,OAAO,CAAC,oBAAoB,CAAC;AAE9C,MAAMC,MAAM,CAAC;EACXC,WAAWA,CAACC,SAAS,EAAEC,OAAO,EAAc;IAAA,IAAZC,MAAM,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;IACxC,IAAI,CAACH,SAAS,GAAGA,SAAS;IAE1B,IAAI,CAACC,OAAO,EAAE;MACZ,IAAI,CAACK,SAAS,GAAG,CAAC;MAClB,IAAI,CAACC,YAAY,GAAG,CAAC;MACrB,IAAI,CAACC,SAAS,GAAG,CAAC;MAClB,IAAI,CAACC,YAAY,GAAG,CAAC;IACvB,CAAC,MAAM,IAAI,OAAOR,OAAO,KAAK,QAAQ,EAAE;MACtC,MAAMS,OAAO,GAAGd,QAAQ,CAACe,aAAa,CAACV,OAAO,CAAC;MAC/C,IAAI,CAACK,SAAS,GAAGI,OAAO,CAACE,GAAG,GAAGV,MAAM;MACrC,IAAI,CAACK,YAAY,GAAG,CAAC;MACrB,IAAI,CAACC,SAAS,GAAGE,OAAO,CAACG,GAAG,GAAGX,MAAM;MACrC,IAAI,CAACO,YAAY,GAAG,CAAC;IACvB,CAAC,MAAM,IAAIR,OAAO,CAACK,SAAS,KAAKD,SAAS,EAAE;MAC1C,IAAI,CAACC,SAAS,GAAGL,OAAO,CAACK,SAAS,IAAI,CAAC;MACvC,IAAI,CAACC,YAAY,GAAGN,OAAO,CAACM,YAAY,IAAI,CAAC;MAC7C,IAAI,CAACC,SAAS,GAAGP,OAAO,CAACO,SAAS,IAAI,CAAC;MACvC,IAAI,CAACC,YAAY,GAAGR,OAAO,CAACQ,YAAY,IAAI,CAAC;IAC/C,CAAC,MAAM,IAAIR,OAAO,CAACW,GAAG,KAAKP,SAAS,EAAE;MACpC,IAAI,CAACO,GAAG,GAAGX,OAAO,CAACW,GAAG,GAAGV,MAAM;MAC/B,IAAI,CAACW,GAAG,GAAGZ,OAAO,CAACY,GAAG,GAAGX,MAAM;IACjC,CAAC,MAAM;MACL,IAAI,CAACI,SAAS,GAAG,CAAC;MAClB,IAAI,CAACC,YAAY,GAAG,CAAC;MACrB,IAAI,CAACC,SAAS,GAAG,CAAC;MAClB,IAAI,CAACC,YAAY,GAAG,CAAC;IACvB;EACF;EAEA,OAAOK,UAAUA,CAACC,KAAK,EAAE;IACvB,OAAOA,KAAK,YAAYjB,MAAM,IAAIiB,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAG,IAAIjB,MAAM,CAACiB,KAAK,CAAC;EAC7E;EAEA,IAAIH,GAAGA,CAAA,EAAG;IACR,OAAO,IAAI,CAACN,SAAS,GAAIU,IAAI,CAACC,GAAG,CAAC,IAAI,CAACC,QAAQ,GAAG,CAAC,EAAE,IAAI,CAACX,YAAY,CAAC,GAAG,IAAI,CAACW,QAAS;EAC1F;EAEA,IAAIN,GAAGA,CAACO,CAAC,EAAE;IACT,IAAI,CAACb,SAAS,GAAGU,IAAI,CAACI,KAAK,CAACD,CAAC,CAAC;IAC9B,IAAI,CAACZ,YAAY,GAAGS,IAAI,CAACI,KAAK,CAAC,CAACD,CAAC,GAAG,IAAI,CAACb,SAAS,IAAI,IAAI,CAACY,QAAQ,CAAC;EACtE;EAEA,IAAIL,GAAGA,CAAA,EAAG;IACR,OAAO,IAAI,CAACL,SAAS,GAAIQ,IAAI,CAACC,GAAG,CAAC,IAAI,CAACI,SAAS,GAAG,CAAC,EAAE,IAAI,CAACZ,YAAY,CAAC,GAAG,IAAI,CAACY,SAAU;EAC5F;EAEA,IAAIR,GAAGA,CAACM,CAAC,EAAE;IACT,IAAI,CAACX,SAAS,GAAGQ,IAAI,CAACI,KAAK,CAACD,CAAC,CAAC;IAC9B,IAAI,CAACV,YAAY,GAAGO,IAAI,CAACI,KAAK,CAAC,CAACD,CAAC,GAAG,IAAI,CAACX,SAAS,IAAI,IAAI,CAACa,SAAS,CAAC;EACvE;EAEA,IAAIH,QAAQA,CAAA,EAAG;IACb,OAAO,IAAI,CAAClB,SAAS,IACnB,IAAI,CAACA,SAAS,CAACsB,SAAS,CAAC,IAAI,CAAChB,SAAS,GAAG,CAAC,CAAC,IAC5C,IAAI,CAACN,SAAS,CAACsB,SAAS,CAAC,IAAI,CAAChB,SAAS,GAAG,CAAC,CAAC,CAACiB,aAAa,GACxDP,IAAI,CAACI,KAAK,CAAC,IAAI,CAACpB,SAAS,CAACsB,SAAS,CAAC,IAAI,CAAChB,SAAS,GAAG,CAAC,CAAC,CAACkB,KAAK,GAAG,KAAK,CAAC,GACtE,MAAM;EACZ;EAEA,IAAIH,SAASA,CAAA,EAAG;IACd,OAAO,IAAI,CAACrB,SAAS,IACnB,IAAI,CAACA,SAAS,CAACyB,MAAM,CAAC,IAAI,CAACjB,SAAS,GAAG,CAAC,CAAC,IACzC,IAAI,CAACR,SAAS,CAACyB,MAAM,CAAC,IAAI,CAACjB,SAAS,GAAG,CAAC,CAAC,CAACkB,MAAM,GAC9CV,IAAI,CAACI,KAAK,CAAC,IAAI,CAACpB,SAAS,CAACyB,MAAM,CAAC,IAAI,CAACjB,SAAS,GAAG,CAAC,CAAC,CAACkB,MAAM,GAAG,KAAK,CAAC,GACpE,MAAM;EACZ;EAEA,IAAIX,KAAKA,CAAA,EAAG;IACV,OAAO;MACLT,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBC,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BC,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBC,YAAY,EAAE,IAAI,CAACA;IACrB,CAAC;EACH;EAEA,IAAIM,KAAKA,CAACY,KAAK,EAAE;IACf,IAAI,CAACrB,SAAS,GAAGqB,KAAK,CAACrB,SAAS;IAChC,IAAI,CAACC,YAAY,GAAGoB,KAAK,CAACpB,YAAY;IACtC,IAAI,CAACC,SAAS,GAAGmB,KAAK,CAACnB,SAAS;IAChC,IAAI,CAACC,YAAY,GAAGkB,KAAK,CAAClB,YAAY;EACxC;AACF;AAEAmB,MAAM,CAACC,OAAO,GAAG/B,MAAM"}