{"version": 3, "file": "csv.js", "names": ["fs", "require", "fastCsv", "customParseFormat", "utc", "dayjs", "extend", "StreamBuf", "exists", "SpecialValues", "true", "false", "error", "CSV", "constructor", "workbook", "worksheet", "readFile", "filename", "options", "Error", "stream", "createReadStream", "read", "close", "Promise", "resolve", "reject", "addWorksheet", "sheetName", "dateFormats", "map", "datum", "datumNumber", "Number", "isNaN", "Infinity", "dt", "reduce", "matchingDate", "currentDateFormat", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Date", "valueOf", "special", "undefined", "csvStream", "parse", "parserOptions", "on", "data", "addRow", "emit", "pipe", "createInputStream", "write", "getWorksheet", "sheetId", "format", "formatterOptions", "dateFormat", "dateUTC", "value", "text", "hyperlink", "formula", "result", "JSON", "stringify", "includeEmptyRows", "lastRow", "eachRow", "row", "rowNumber", "values", "shift", "end", "writeFile", "streamOptions", "encoding", "createWriteStream", "writeBuffer", "module", "exports"], "sources": ["../../../lib/csv/csv.js"], "sourcesContent": ["const fs = require('fs');\nconst fastCsv = require('fast-csv');\nconst customParseFormat = require('dayjs/plugin/customParseFormat');\nconst utc = require('dayjs/plugin/utc');\nconst dayjs = require('dayjs').extend(customParseFormat).extend(utc);\nconst StreamBuf = require('../utils/stream-buf');\n\nconst {\n  fs: {exists},\n} = require('../utils/utils');\n\n/* eslint-disable quote-props */\nconst SpecialValues = {\n  true: true,\n  false: false,\n  '#N/A': {error: '#N/A'},\n  '#REF!': {error: '#REF!'},\n  '#NAME?': {error: '#NAME?'},\n  '#DIV/0!': {error: '#DIV/0!'},\n  '#NULL!': {error: '#NULL!'},\n  '#VALUE!': {error: '#VALUE!'},\n  '#NUM!': {error: '#NUM!'},\n};\n/* eslint-ensable quote-props */\n\nclass CSV {\n  constructor(workbook) {\n    this.workbook = workbook;\n    this.worksheet = null;\n  }\n\n  async readFile(filename, options) {\n    options = options || {};\n    if (!(await exists(filename))) {\n      throw new Error(`File not found: ${filename}`);\n    }\n    const stream = fs.createReadStream(filename);\n    const worksheet = await this.read(stream, options);\n    stream.close();\n    return worksheet;\n  }\n\n  read(stream, options) {\n    options = options || {};\n\n    return new Promise((resolve, reject) => {\n      const worksheet = this.workbook.addWorksheet(options.sheetName);\n\n      const dateFormats = options.dateFormats || [\n        'YYYY-MM-DD[T]HH:mm:ssZ',\n        'YYYY-MM-DD[T]HH:mm:ss',\n        'MM-DD-YYYY',\n        'YYYY-MM-DD',\n      ];\n      const map =\n        options.map ||\n        function(datum) {\n          if (datum === '') {\n            return null;\n          }\n          const datumNumber = Number(datum);\n          if (!Number.isNaN(datumNumber) && datumNumber !== Infinity) {\n            return datumNumber;\n          }\n          const dt = dateFormats.reduce((matchingDate, currentDateFormat) => {\n            if (matchingDate) {\n              return matchingDate;\n            }\n            const dayjsObj = dayjs(datum, currentDateFormat, true);\n            if (dayjsObj.isValid()) {\n              return dayjsObj;\n            }\n            return null;\n          }, null);\n          if (dt) {\n            return new Date(dt.valueOf());\n          }\n          const special = SpecialValues[datum];\n          if (special !== undefined) {\n            return special;\n          }\n          return datum;\n        };\n\n      const csvStream = fastCsv\n        .parse(options.parserOptions)\n        .on('data', data => {\n          worksheet.addRow(data.map(map));\n        })\n        .on('end', () => {\n          csvStream.emit('worksheet', worksheet);\n        });\n\n      csvStream.on('worksheet', resolve).on('error', reject);\n\n      stream.pipe(csvStream);\n    });\n  }\n\n  /**\n   * @deprecated since version 4.0. You should use `CSV#read` instead. Please follow upgrade instruction: https://github.com/exceljs/exceljs/blob/master/UPGRADE-4.0.md\n   */\n  createInputStream() {\n    throw new Error(\n      '`CSV#createInputStream` is deprecated. You should use `CSV#read` instead. This method will be removed in version 5.0. Please follow upgrade instruction: https://github.com/exceljs/exceljs/blob/master/UPGRADE-4.0.md'\n    );\n  }\n\n  write(stream, options) {\n    return new Promise((resolve, reject) => {\n      options = options || {};\n      // const encoding = options.encoding || 'utf8';\n      // const separator = options.separator || ',';\n      // const quoteChar = options.quoteChar || '\\'';\n\n      const worksheet = this.workbook.getWorksheet(options.sheetName || options.sheetId);\n\n      const csvStream = fastCsv.format(options.formatterOptions);\n      stream.on('finish', () => {\n        resolve();\n      });\n      csvStream.on('error', reject);\n      csvStream.pipe(stream);\n\n      const {dateFormat, dateUTC} = options;\n      const map =\n        options.map ||\n        (value => {\n          if (value) {\n            if (value.text || value.hyperlink) {\n              return value.hyperlink || value.text || '';\n            }\n            if (value.formula || value.result) {\n              return value.result || '';\n            }\n            if (value instanceof Date) {\n              if (dateFormat) {\n                return dateUTC\n                  ? dayjs.utc(value).format(dateFormat)\n                  : dayjs(value).format(dateFormat);\n              }\n              return dateUTC ? dayjs.utc(value).format() : dayjs(value).format();\n            }\n            if (value.error) {\n              return value.error;\n            }\n            if (typeof value === 'object') {\n              return JSON.stringify(value);\n            }\n          }\n          return value;\n        });\n\n      const includeEmptyRows = options.includeEmptyRows === undefined || options.includeEmptyRows;\n      let lastRow = 1;\n      if (worksheet) {\n        worksheet.eachRow((row, rowNumber) => {\n          if (includeEmptyRows) {\n            while (lastRow++ < rowNumber - 1) {\n              csvStream.write([]);\n            }\n          }\n          const {values} = row;\n          values.shift();\n          csvStream.write(values.map(map));\n          lastRow = rowNumber;\n        });\n      }\n      csvStream.end();\n    });\n  }\n\n  writeFile(filename, options) {\n    options = options || {};\n\n    const streamOptions = {\n      encoding: options.encoding || 'utf8',\n    };\n    const stream = fs.createWriteStream(filename, streamOptions);\n\n    return this.write(stream, options);\n  }\n\n  async writeBuffer(options) {\n    const stream = new StreamBuf();\n    await this.write(stream, options);\n    return stream.read();\n  }\n}\n\nmodule.exports = CSV;\n"], "mappings": ";;AAAA,MAAMA,EAAE,GAAGC,OAAO,CAAC,IAAI,CAAC;AACxB,MAAMC,OAAO,GAAGD,OAAO,CAAC,UAAU,CAAC;AACnC,MAAME,iBAAiB,GAAGF,OAAO,CAAC,gCAAgC,CAAC;AACnE,MAAMG,GAAG,GAAGH,OAAO,CAAC,kBAAkB,CAAC;AACvC,MAAMI,KAAK,GAAGJ,OAAO,CAAC,OAAO,CAAC,CAACK,MAAM,CAACH,iBAAiB,CAAC,CAACG,MAAM,CAACF,GAAG,CAAC;AACpE,MAAMG,SAAS,GAAGN,OAAO,CAAC,qBAAqB,CAAC;AAEhD,MAAM;EACJD,EAAE,EAAE;IAACQ;EAAM;AACb,CAAC,GAAGP,OAAO,CAAC,gBAAgB,CAAC;;AAE7B;AACA,MAAMQ,aAAa,GAAG;EACpBC,IAAI,EAAE,IAAI;EACVC,KAAK,EAAE,KAAK;EACZ,MAAM,EAAE;IAACC,KAAK,EAAE;EAAM,CAAC;EACvB,OAAO,EAAE;IAACA,KAAK,EAAE;EAAO,CAAC;EACzB,QAAQ,EAAE;IAACA,KAAK,EAAE;EAAQ,CAAC;EAC3B,SAAS,EAAE;IAACA,KAAK,EAAE;EAAS,CAAC;EAC7B,QAAQ,EAAE;IAACA,KAAK,EAAE;EAAQ,CAAC;EAC3B,SAAS,EAAE;IAACA,KAAK,EAAE;EAAS,CAAC;EAC7B,OAAO,EAAE;IAACA,KAAK,EAAE;EAAO;AAC1B,CAAC;AACD;;AAEA,MAAMC,GAAG,CAAC;EACRC,WAAWA,CAACC,QAAQ,EAAE;IACpB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,SAAS,GAAG,IAAI;EACvB;EAEA,MAAMC,QAAQA,CAACC,QAAQ,EAAEC,OAAO,EAAE;IAChCA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;IACvB,IAAI,EAAE,MAAMX,MAAM,CAACU,QAAQ,CAAC,CAAC,EAAE;MAC7B,MAAM,IAAIE,KAAK,CAAE,mBAAkBF,QAAS,EAAC,CAAC;IAChD;IACA,MAAMG,MAAM,GAAGrB,EAAE,CAACsB,gBAAgB,CAACJ,QAAQ,CAAC;IAC5C,MAAMF,SAAS,GAAG,MAAM,IAAI,CAACO,IAAI,CAACF,MAAM,EAAEF,OAAO,CAAC;IAClDE,MAAM,CAACG,KAAK,CAAC,CAAC;IACd,OAAOR,SAAS;EAClB;EAEAO,IAAIA,CAACF,MAAM,EAAEF,OAAO,EAAE;IACpBA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;IAEvB,OAAO,IAAIM,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtC,MAAMX,SAAS,GAAG,IAAI,CAACD,QAAQ,CAACa,YAAY,CAACT,OAAO,CAACU,SAAS,CAAC;MAE/D,MAAMC,WAAW,GAAGX,OAAO,CAACW,WAAW,IAAI,CACzC,wBAAwB,EACxB,uBAAuB,EACvB,YAAY,EACZ,YAAY,CACb;MACD,MAAMC,GAAG,GACPZ,OAAO,CAACY,GAAG,IACX,UAASC,KAAK,EAAE;QACd,IAAIA,KAAK,KAAK,EAAE,EAAE;UAChB,OAAO,IAAI;QACb;QACA,MAAMC,WAAW,GAAGC,MAAM,CAACF,KAAK,CAAC;QACjC,IAAI,CAACE,MAAM,CAACC,KAAK,CAACF,WAAW,CAAC,IAAIA,WAAW,KAAKG,QAAQ,EAAE;UAC1D,OAAOH,WAAW;QACpB;QACA,MAAMI,EAAE,GAAGP,WAAW,CAACQ,MAAM,CAAC,CAACC,YAAY,EAAEC,iBAAiB,KAAK;UACjE,IAAID,YAAY,EAAE;YAChB,OAAOA,YAAY;UACrB;UACA,MAAME,QAAQ,GAAGpC,KAAK,CAAC2B,KAAK,EAAEQ,iBAAiB,EAAE,IAAI,CAAC;UACtD,IAAIC,QAAQ,CAACC,OAAO,CAAC,CAAC,EAAE;YACtB,OAAOD,QAAQ;UACjB;UACA,OAAO,IAAI;QACb,CAAC,EAAE,IAAI,CAAC;QACR,IAAIJ,EAAE,EAAE;UACN,OAAO,IAAIM,IAAI,CAACN,EAAE,CAACO,OAAO,CAAC,CAAC,CAAC;QAC/B;QACA,MAAMC,OAAO,GAAGpC,aAAa,CAACuB,KAAK,CAAC;QACpC,IAAIa,OAAO,KAAKC,SAAS,EAAE;UACzB,OAAOD,OAAO;QAChB;QACA,OAAOb,KAAK;MACd,CAAC;MAEH,MAAMe,SAAS,GAAG7C,OAAO,CACtB8C,KAAK,CAAC7B,OAAO,CAAC8B,aAAa,CAAC,CAC5BC,EAAE,CAAC,MAAM,EAAEC,IAAI,IAAI;QAClBnC,SAAS,CAACoC,MAAM,CAACD,IAAI,CAACpB,GAAG,CAACA,GAAG,CAAC,CAAC;MACjC,CAAC,CAAC,CACDmB,EAAE,CAAC,KAAK,EAAE,MAAM;QACfH,SAAS,CAACM,IAAI,CAAC,WAAW,EAAErC,SAAS,CAAC;MACxC,CAAC,CAAC;MAEJ+B,SAAS,CAACG,EAAE,CAAC,WAAW,EAAExB,OAAO,CAAC,CAACwB,EAAE,CAAC,OAAO,EAAEvB,MAAM,CAAC;MAEtDN,MAAM,CAACiC,IAAI,CAACP,SAAS,CAAC;IACxB,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;EACEQ,iBAAiBA,CAAA,EAAG;IAClB,MAAM,IAAInC,KAAK,CACb,wNACF,CAAC;EACH;EAEAoC,KAAKA,CAACnC,MAAM,EAAEF,OAAO,EAAE;IACrB,OAAO,IAAIM,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtCR,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;MACvB;MACA;MACA;;MAEA,MAAMH,SAAS,GAAG,IAAI,CAACD,QAAQ,CAAC0C,YAAY,CAACtC,OAAO,CAACU,SAAS,IAAIV,OAAO,CAACuC,OAAO,CAAC;MAElF,MAAMX,SAAS,GAAG7C,OAAO,CAACyD,MAAM,CAACxC,OAAO,CAACyC,gBAAgB,CAAC;MAC1DvC,MAAM,CAAC6B,EAAE,CAAC,QAAQ,EAAE,MAAM;QACxBxB,OAAO,CAAC,CAAC;MACX,CAAC,CAAC;MACFqB,SAAS,CAACG,EAAE,CAAC,OAAO,EAAEvB,MAAM,CAAC;MAC7BoB,SAAS,CAACO,IAAI,CAACjC,MAAM,CAAC;MAEtB,MAAM;QAACwC,UAAU;QAAEC;MAAO,CAAC,GAAG3C,OAAO;MACrC,MAAMY,GAAG,GACPZ,OAAO,CAACY,GAAG,KACVgC,KAAK,IAAI;QACR,IAAIA,KAAK,EAAE;UACT,IAAIA,KAAK,CAACC,IAAI,IAAID,KAAK,CAACE,SAAS,EAAE;YACjC,OAAOF,KAAK,CAACE,SAAS,IAAIF,KAAK,CAACC,IAAI,IAAI,EAAE;UAC5C;UACA,IAAID,KAAK,CAACG,OAAO,IAAIH,KAAK,CAACI,MAAM,EAAE;YACjC,OAAOJ,KAAK,CAACI,MAAM,IAAI,EAAE;UAC3B;UACA,IAAIJ,KAAK,YAAYpB,IAAI,EAAE;YACzB,IAAIkB,UAAU,EAAE;cACd,OAAOC,OAAO,GACVzD,KAAK,CAACD,GAAG,CAAC2D,KAAK,CAAC,CAACJ,MAAM,CAACE,UAAU,CAAC,GACnCxD,KAAK,CAAC0D,KAAK,CAAC,CAACJ,MAAM,CAACE,UAAU,CAAC;YACrC;YACA,OAAOC,OAAO,GAAGzD,KAAK,CAACD,GAAG,CAAC2D,KAAK,CAAC,CAACJ,MAAM,CAAC,CAAC,GAAGtD,KAAK,CAAC0D,KAAK,CAAC,CAACJ,MAAM,CAAC,CAAC;UACpE;UACA,IAAII,KAAK,CAACnD,KAAK,EAAE;YACf,OAAOmD,KAAK,CAACnD,KAAK;UACpB;UACA,IAAI,OAAOmD,KAAK,KAAK,QAAQ,EAAE;YAC7B,OAAOK,IAAI,CAACC,SAAS,CAACN,KAAK,CAAC;UAC9B;QACF;QACA,OAAOA,KAAK;MACd,CAAC,CAAC;MAEJ,MAAMO,gBAAgB,GAAGnD,OAAO,CAACmD,gBAAgB,KAAKxB,SAAS,IAAI3B,OAAO,CAACmD,gBAAgB;MAC3F,IAAIC,OAAO,GAAG,CAAC;MACf,IAAIvD,SAAS,EAAE;QACbA,SAAS,CAACwD,OAAO,CAAC,CAACC,GAAG,EAAEC,SAAS,KAAK;UACpC,IAAIJ,gBAAgB,EAAE;YACpB,OAAOC,OAAO,EAAE,GAAGG,SAAS,GAAG,CAAC,EAAE;cAChC3B,SAAS,CAACS,KAAK,CAAC,EAAE,CAAC;YACrB;UACF;UACA,MAAM;YAACmB;UAAM,CAAC,GAAGF,GAAG;UACpBE,MAAM,CAACC,KAAK,CAAC,CAAC;UACd7B,SAAS,CAACS,KAAK,CAACmB,MAAM,CAAC5C,GAAG,CAACA,GAAG,CAAC,CAAC;UAChCwC,OAAO,GAAGG,SAAS;QACrB,CAAC,CAAC;MACJ;MACA3B,SAAS,CAAC8B,GAAG,CAAC,CAAC;IACjB,CAAC,CAAC;EACJ;EAEAC,SAASA,CAAC5D,QAAQ,EAAEC,OAAO,EAAE;IAC3BA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;IAEvB,MAAM4D,aAAa,GAAG;MACpBC,QAAQ,EAAE7D,OAAO,CAAC6D,QAAQ,IAAI;IAChC,CAAC;IACD,MAAM3D,MAAM,GAAGrB,EAAE,CAACiF,iBAAiB,CAAC/D,QAAQ,EAAE6D,aAAa,CAAC;IAE5D,OAAO,IAAI,CAACvB,KAAK,CAACnC,MAAM,EAAEF,OAAO,CAAC;EACpC;EAEA,MAAM+D,WAAWA,CAAC/D,OAAO,EAAE;IACzB,MAAME,MAAM,GAAG,IAAId,SAAS,CAAC,CAAC;IAC9B,MAAM,IAAI,CAACiD,KAAK,CAACnC,MAAM,EAAEF,OAAO,CAAC;IACjC,OAAOE,MAAM,CAACE,IAAI,CAAC,CAAC;EACtB;AACF;AAEA4D,MAAM,CAACC,OAAO,GAAGvE,GAAG"}