/**
 * 模态框布局优化模块
 * 专门解决总监审核模态框中经理选择区域导致的布局问题
 */

// 模态框布局优化配置
const MODAL_CONFIG = {
    // 模态框最大高度（视口高度的百分比）
    maxHeightPercent: 95,
    // 审批操作区域最大高度（视口高度的百分比）
    actionsMaxHeightPercent: 40,
    // 经理选择区域最大高度
    managersSelectionMaxHeight: 200,
    // 经理列表最大高度
    managersListMaxHeight: 120
};

/**
 * 优化详情模态框布局
 */
function optimizeDetailModalLayout() {
    const detailModal = document.getElementById('detailModal');
    if (!detailModal) return;

    const modalContainer = detailModal.querySelector('.relative');
    if (!modalContainer) return;

    // 应用优化样式类
    modalContainer.classList.add('detail-modal-optimized');

    // 优化内容区域
    const contentArea = modalContainer.querySelector('.flex-grow');
    if (contentArea) {
        contentArea.classList.add('detail-modal-content');
    }

    // 优化审批操作区域
    const actionsArea = document.getElementById('detail-approval-actions');
    if (actionsArea) {
        actionsArea.classList.add('detail-modal-actions');
    }

    // 优化经理选择区域
    const managersSelection = document.getElementById('detail-managers-selection');
    if (managersSelection) {
        managersSelection.classList.add('managers-selection-optimized');

        // 优化经理列表
        const managersList = document.getElementById('detail-managers-list');
        if (managersList) {
            managersList.classList.add('managers-list-compact');
        }
    }

    console.log('详情模态框布局已优化');
}

/**
 * 动态调整模态框高度
 */
function adjustModalDynamicHeight() {
    const detailModal = document.getElementById('detailModal');
    if (!detailModal || detailModal.classList.contains('hidden')) return;

    const modalContainer = detailModal.querySelector('.relative');
    if (!modalContainer) return;

    const viewportHeight = window.innerHeight;

    // 设置模态框最大高度
    const maxModalHeight = viewportHeight * (MODAL_CONFIG.maxHeightPercent / 100);
    modalContainer.style.maxHeight = `${maxModalHeight}px`;

    // 设置审批操作区域最大高度
    const actionsArea = document.getElementById('detail-approval-actions');
    if (actionsArea && !actionsArea.classList.contains('hidden')) {
        const maxActionsHeight = viewportHeight * (MODAL_CONFIG.actionsMaxHeightPercent / 100);
        actionsArea.style.maxHeight = `${maxActionsHeight}px`;
    }

    // 设置经理选择区域高度
    const managersSelection = document.getElementById('detail-managers-selection');
    if (managersSelection && !managersSelection.classList.contains('hidden')) {
        managersSelection.style.maxHeight = `${MODAL_CONFIG.managersSelectionMaxHeight}px`;

        const managersList = document.getElementById('detail-managers-list');
        if (managersList) {
            managersList.style.maxHeight = `${MODAL_CONFIG.managersListMaxHeight}px`;
        }
    }

    console.log(`模态框高度已调整: 最大高度 ${maxModalHeight}px`);
}

/**
 * 检查是否需要特殊布局优化（仅总监审批且有经理选择区域时）
 */
function needsSpecialLayout() {
    const managersSelection = document.getElementById('detail-managers-selection');
    const actionsArea = document.getElementById('detail-approval-actions');

    // 只有当经理选择区域存在且可见，且审批操作区域也可见时，才需要特殊布局
    return managersSelection &&
           !managersSelection.classList.contains('hidden') &&
           actionsArea &&
           !actionsArea.classList.contains('hidden');
}

/**
 * 应用标准布局（适用于厂长、经理、CEO等其他角色）
 */
function applyStandardLayout() {
    const detailModal = document.getElementById('detailModal');
    if (!detailModal) return;

    const modalContainer = detailModal.querySelector('.relative');
    const contentArea = modalContainer?.querySelector('.flex-grow');
    const actionsArea = document.getElementById('detail-approval-actions');

    if (!modalContainer || !contentArea) return;

    // 移除可能存在的特殊布局类
    modalContainer.classList.remove('detail-modal-optimized');

    // 添加标准布局类
    modalContainer.classList.add('modal-standard-layout');

    // 重置内联样式，让CSS类控制布局
    modalContainer.style.height = '';
    modalContainer.style.maxHeight = '';
    modalContainer.style.display = '';
    modalContainer.style.flexDirection = '';
    modalContainer.style.gridTemplateRows = '';

    contentArea.style.height = '';
    contentArea.style.maxHeight = '';
    contentArea.style.flex = '';
    contentArea.style.overflowY = '';

    if (actionsArea) {
        actionsArea.style.height = '';
        actionsArea.style.maxHeight = '';
        actionsArea.style.flexShrink = '';
        actionsArea.style.overflowY = '';
    }

    console.log('已应用标准布局（适用于厂长、经理、CEO等）');
}

/**
 * 智能布局调整 - 根据内容动态分配空间
 */
function smartLayoutAdjustment() {
    const detailModal = document.getElementById('detailModal');
    if (!detailModal || detailModal.classList.contains('hidden')) return;

    // 检查是否需要特殊布局优化
    if (!needsSpecialLayout()) {
        // 对于其他角色，使用标准布局
        applyStandardLayout();
        return;
    }

    // 以下是总监审批的特殊布局逻辑
    const modalContainer = detailModal.querySelector('.relative');
    const contentArea = modalContainer?.querySelector('.flex-grow');
    const actionsArea = document.getElementById('detail-approval-actions');
    const managersSelection = document.getElementById('detail-managers-selection');

    if (!modalContainer || !contentArea) return;

    // 移除标准布局类，应用特殊布局类
    modalContainer.classList.remove('modal-standard-layout');
    modalContainer.classList.add('detail-modal-optimized');

    const viewportHeight = window.innerHeight;
    const modalPadding = 32; // 模态框上下边距
    const headerHeight = 60; // 标题栏高度
    const tabsHeight = 48; // 标签栏高度
    const actionsPadding = 24; // 审批操作区域内边距

    // 计算可用高度
    let availableHeight = viewportHeight * 0.95 - modalPadding - headerHeight - tabsHeight;

    // 如果显示审批操作区域，计算其所需高度
    let actionsHeight = 0;
    if (actionsArea && !actionsArea.classList.contains('hidden')) {
        // 基础审批操作高度（备注框 + 按钮）
        actionsHeight = 120 + actionsPadding;

        // 如果显示经理选择区域，增加高度
        if (managersSelection && !managersSelection.classList.contains('hidden')) {
            actionsHeight += MODAL_CONFIG.managersSelectionMaxHeight;
        }

        // 限制审批操作区域最大高度
        const maxActionsHeight = viewportHeight * (MODAL_CONFIG.actionsMaxHeightPercent / 100);
        actionsHeight = Math.min(actionsHeight, maxActionsHeight);
    }

    // 计算内容区域高度
    const contentHeight = availableHeight - actionsHeight;

    // 应用高度设置
    contentArea.style.height = `${contentHeight}px`;
    contentArea.style.maxHeight = `${contentHeight}px`;

    if (actionsArea && actionsHeight > 0) {
        actionsArea.style.height = `${actionsHeight}px`;
        actionsArea.style.maxHeight = `${actionsHeight}px`;
    }

    console.log(`总监特殊布局调整完成: 内容区域 ${contentHeight}px, 操作区域 ${actionsHeight}px`);
}

/**
 * 处理经理选择区域的显示/隐藏
 */
function handleManagersSelectionToggle() {
    const managersSelection = document.getElementById('detail-managers-selection');
    if (!managersSelection) return;

    // 创建观察器监听经理选择区域的显示状态变化
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                // 当经理选择区域显示状态改变时，重新调整布局
                setTimeout(() => {
                    smartLayoutAdjustment();
                }, 10);
            }
        });
    });

    observer.observe(managersSelection, {
        attributes: true,
        attributeFilter: ['class']
    });

    console.log('经理选择区域状态监听器已设置');
}

/**
 * 初始化模态框布局优化
 */
function initModalLayoutOptimizer() {
    // 优化模态框布局
    optimizeDetailModalLayout();

    // 设置经理选择区域状态监听
    handleManagersSelectionToggle();

    // 增强经理选择相关函数
    enhanceManagersSelectionFunctions();

    // 窗口大小变化时重新调整
    window.addEventListener('resize', () => {
        setTimeout(() => {
            adjustModalDynamicHeight();
            smartLayoutAdjustment();
        }, 100);
    });

    console.log('模态框布局优化器已初始化');
}

/**
 * 重写showDetail函数以集成布局优化
 */
function enhanceShowDetailFunction() {
    // 保存原始函数
    const originalShowDetail = window.showDetail;

    if (typeof originalShowDetail === 'function') {
        window.showDetail = function(appId) {
            // 调用原始函数
            originalShowDetail(appId);

            // 延迟执行布局优化，确保DOM已更新
            setTimeout(() => {
                // 首先重置经理选择区域状态
                resetManagersSelectionState();

                // 然后应用布局优化
                optimizeDetailModalLayout();
                adjustModalDynamicHeight();
                smartLayoutAdjustment();
            }, 50);
        };

        console.log('showDetail函数已增强，集成布局优化和状态重置');
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 延迟初始化，确保其他脚本已加载
    setTimeout(() => {
        initModalLayoutOptimizer();
        enhanceShowDetailFunction();
    }, 100);
});

/**
 * 重置经理选择区域到默认折叠状态
 */
function resetManagersSelectionState() {
    const content = document.getElementById('managers-selection-content');
    const icon = document.getElementById('managers-toggle-icon');
    const summary = document.getElementById('detail-selected-managers-summary');

    if (!content || !icon || !summary) return;

    // 强制设置为折叠状态
    content.classList.add('hidden');
    icon.style.transform = 'rotate(0deg)';
    summary.textContent = '点击展开选择';
    summary.classList.remove('text-green-600');

    // 重置确认状态
    const confirmStatus = document.getElementById('detail-managers-confirm-status');
    if (confirmStatus) {
        confirmStatus.classList.add('hidden');
    }

    // 重置选择计数
    const countElement = document.getElementById('detail-selected-managers-count');
    if (countElement) {
        countElement.textContent = '已选择 0 位经理';
        countElement.classList.remove('text-green-600');
    }

    console.log('经理选择区域已重置为默认折叠状态');
}

/**
 * 切换经理选择区域的折叠状态
 */
function toggleManagersSelection() {
    const content = document.getElementById('managers-selection-content');
    const icon = document.getElementById('managers-toggle-icon');
    const summary = document.getElementById('detail-selected-managers-summary');

    if (!content || !icon || !summary) return;

    const isHidden = content.classList.contains('hidden');

    if (isHidden) {
        // 展开
        content.classList.remove('hidden');
        icon.style.transform = 'rotate(180deg)';
        summary.textContent = '点击收起';
    } else {
        // 收起
        content.classList.add('hidden');
        icon.style.transform = 'rotate(0deg)';

        // 更新摘要信息
        const selectedCount = document.getElementById('detail-selected-managers-count')?.textContent || '已选择 0 位经理';
        const isConfirmed = document.getElementById('detail-managers-confirm-status')?.classList.contains('hidden') === false;

        if (isConfirmed) {
            summary.textContent = selectedCount + ' ✓';
            summary.classList.add('text-green-600');
        } else {
            summary.textContent = selectedCount;
            summary.classList.remove('text-green-600');
        }
    }

    // 重新调整布局
    setTimeout(() => {
        smartLayoutAdjustment();
    }, 300); // 等待动画完成
}

/**
 * 更新经理选择摘要信息
 */
function updateManagersSelectionSummary() {
    const summary = document.getElementById('detail-selected-managers-summary');
    const content = document.getElementById('managers-selection-content');

    if (!summary || !content) return;

    // 如果内容区域是展开的，不更新摘要
    if (!content.classList.contains('hidden')) {
        summary.textContent = '点击收起';
        return;
    }

    // 获取选择状态
    const selectedCount = document.getElementById('detail-selected-managers-count')?.textContent || '已选择 0 位经理';
    const isConfirmed = document.getElementById('detail-managers-confirm-status')?.classList.contains('hidden') === false;

    if (isConfirmed) {
        summary.textContent = selectedCount + ' ✓';
        summary.classList.add('text-green-600');
    } else {
        summary.textContent = selectedCount;
        summary.classList.remove('text-green-600');
    }
}

/**
 * 增强现有的经理选择相关函数
 */
function enhanceManagersSelectionFunctions() {
    // 增强updateSelectedManagersCount函数
    const originalUpdateSelectedManagersCount = window.updateSelectedManagersCount;
    if (typeof originalUpdateSelectedManagersCount === 'function') {
        window.updateSelectedManagersCount = function(appId) {
            originalUpdateSelectedManagersCount(appId);
            // 更新摘要信息
            setTimeout(() => {
                updateManagersSelectionSummary();
            }, 10);
        };
    }

    // 增强confirmSelectedManagers函数
    const originalConfirmSelectedManagers = window.confirmSelectedManagers;
    if (typeof originalConfirmSelectedManagers === 'function') {
        window.confirmSelectedManagers = function(appId) {
            originalConfirmSelectedManagers(appId);
            // 更新摘要信息
            setTimeout(() => {
                updateManagersSelectionSummary();
            }, 10);
        };
    }

    console.log('经理选择相关函数已增强');
}

// 导出函数供外部调用
window.modalLayoutOptimizer = {
    optimize: optimizeDetailModalLayout,
    adjust: adjustModalDynamicHeight,
    smartAdjust: smartLayoutAdjustment,
    standardLayout: applyStandardLayout,
    needsSpecial: needsSpecialLayout,
    init: initModalLayoutOptimizer,
    toggleManagers: toggleManagersSelection,
    updateSummary: updateManagersSelectionSummary,
    resetManagersState: resetManagersSelectionState
};

// 将函数设为全局函数，供HTML调用
window.toggleManagersSelection = toggleManagersSelection;
window.resetManagersSelectionState = resetManagersSelectionState;
